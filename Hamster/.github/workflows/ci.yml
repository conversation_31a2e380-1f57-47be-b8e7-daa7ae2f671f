name: Build Test
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: macos-13
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: true

      - name: Download Frameworks
        uses: robinraju/release-downloader@v1.7
        with:
          repository: "imfuxiao/LibrimeKit"
          latest: true
          fileName: "Frameworks.tgz"

      - name: Un-tar Frameworks
        run: |
          tar -xzf Frameworks.tgz -C Frameworks/..

      - name: Build Schemas
        run: |
          make schema

      - name: Select Xcode
        run: sudo xcode-select -s "/Applications/Xcode_15.0.1.app"

      - name: Compile
        run: |
          xcodebuild archive -archivePath "Hamster" -scheme "Hamster" -sdk "iphoneos" -arch arm64 -configuration Release CODE_SIGNING_ALLOWED=NO
          BUILT_PATH=$(find Hamster.xcarchive -name '*.app' -type d | head -1)
          find "$BUILT_PATH" -type d -path '*/Frameworks/*.dylib' -exec codesign --force --sign - --timestamp=none \{\} \;
          codesign --force --sign - --entitlements "Hamster/Hamster.entitlements" --timestamp=none "$BUILT_PATH"
          tar -acf Hamster.xcarchive.tgz Hamster.xcarchive

      - name: Upload xcarchive
        uses: actions/upload-artifact@v3
        with:
          name: Hamster-ios-arm64-xcarchive
          path: Hamster.xcarchive.tgz
