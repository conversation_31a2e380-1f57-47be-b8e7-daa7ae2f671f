<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>zip</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.pkware.zip-archive</string>
				<string>public.archive</string>
			</array>
		</dict>
	</array>
	<key>CFBundleGetInfoString</key>
	<string></string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleURLName</key>
			<string>dev.fuxiao.app.hamster</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>hamster</string>
			</array>
		</dict>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSUbiquitousContainers</key>
	<dict>
		<key>iCloud.dev.fuxiao.app.hamsterapp</key>
		<dict>
			<key>NSUbiquitousContainerIsDocumentScopePublic</key>
			<true/>
			<key>NSUbiquitousContainerName</key>
			<string>Hamster</string>
			<key>NSUbiquitousContainerSupportedFolderLevels</key>
			<string>Any</string>
		</dict>
	</dict>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UIRequiresPersistentWiFi</key>
	<true/>
	<key>rimeVersion</key>
	<string>1.9.0</string>
</dict>
</plist>
