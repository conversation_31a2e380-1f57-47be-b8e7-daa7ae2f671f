//
//  File.swift
//
//
//  Created by morse on 2023/9/5.
//

import Foundation

/// 符号分类
enum SymbolCategory: String, CaseIterable, Codable, SymbolProvider, Identifiable, Hashable {
  // 常用符号
  case frequent
  
  // 英文符号
  case ascii
  
  // 中文符号
  case cn
  
  // 数学符号
  case math
  
  // 特殊符号
  case special
  
  // 单位
  case unit
  
  // 列表
  case list
  
  // 拼音
  case pinyin
  
  // 注音
  case bopomofo
  
  // 部首
  case radical
  
  // 希腊
  case grease
  
  // 俄语
  case rusa
  
  // 拉丁
  case lation
  
  // 韩文
  case korea
  
  // 音标
  case phonetic
  
  // 颜文字
//  case kaomoji
  
  // 假名
  case jp
  
  static var frequentSymbolProvider: FrequentSymbolProvider = MostRecentSymbolProvider()
}

extension SymbolCategory {
  static var all: [SymbolCategory] { allCases }
}

extension SymbolCategory {
  var id: String { rawValue }
  
  var string: String {
    switch self {
    case .frequent: return "常用"
    case .ascii: return "英文"
    case .cn: return "中文"
    case .math: return "数学"
    case .special: return "特殊"
    case .unit: return "单位"
    case .list: return "列表"
    case .pinyin: return "拼音"
    case .bopomofo: return "注音"
    case .radical: return "部首"
    case .grease: return "希腊"
    case .rusa: return "俄语"
    case .lation: return "拉丁"
    case .korea: return "韩文"
    case .phonetic: return "音标"
//    case .kaomoji: return "颜文字"
    case .jp: return "假名"
    }
  }
  
  var symbols: [Symbol] {
    symbolsString
      .compactMap { Symbol(char: String($0)) }
  }
  
  var symbolsString: [String] {
    switch self {
    case .frequent:
      return Self.frequentSymbolProvider.symbols.map { $0.char }
      
    // 英文符号
    case .ascii: return ",.?!:;/\\|*-+=^$`'\"^~@#%&()[]{}_¡£€¿¢฿♀♂".chars
      
    // 中文符号
    case .cn:
      return ["，", "。", "？", "！", "、", "：", "；", "“", "”", "‘", "’", "···", "……", "-", "——", "（", " ）", "【", "】", "〖", "〗", "《", " 》", "［", "］", "'", "｛", "｝", "「", "」", "『", "』", "〈", "〉", "～", "·", "＃", "＊", "|", "￥", "&", "‖", "〈", "〉", "«", "»"]
      
    case .math:
      return "≈＝≠≌<>≤≥≡()[]{}-+±×*/÷&∥%‰‱°′″∫∮∯∬∭∰∞∑∧∏∈∵∴⊥∝∨∪•√〒∝∽∈∩∧⊙⌒∥∟∣∂∆∞≌∉∪∨⊕⊿⊥∠∫∬∭".chars + ["℃", "℉", "㎎", "㎏", "μm", "㎜", "㎝", "㎞", "㎡", "m³", "㏄", "ml", "mol", "㏕", "sin", "cos", "tan", "cot", "lim"]
      
    // 特殊符号
    case .special:
      return "△▽○◇□☆▲▼●◆■★▷◁▶◀♻♲†⚝✡⚹✦✸✹�×⌫☑☒✅❎✔✘✓✗☼☽♀☻◐㏂☀☾♂☹◑㏘☜☝☞☚☟☛▪•‥…∷※♩♪♫♬§°♭♯♮‖¶№◎¤۞℗®©卍卐℡™㏇Φ↖↑↗◤㊤◥←↔→㊧㊥㊨↙↓↘◣㊦◢⇄⇅⇆⇤↩⇥❏❐◲〼▢▣⇦⇧⇨⇩⇪↶▸◂▴▾✁↷✍⏍ϟ📝✎✆☱☰☴⚿⛮⚙☲☯☵⛶☩☐☳☷☶💬🗨⟲ღ✈☂🎤🌐🔍".chars
      
    // 单位
    case .unit:
      return "℃¥$€฿￡㎡m³℉￥£￠₠¹²³⁴⁵ⁿ⁶⁷⁸⁹⁰ˣ⁺⁻⁼⁽⁾½⅓¼⅔¾₁₂₃₄₅ₙ₆₇₈₉₀ₓ₊₋₌₍₎℅".chars
      
    // 列表
    case .list:
      return "①②③④⑤⑥⑦⑧⑨⑩⒈⒉⒊⒋⒌⒍⒎⒏⒐⒑⒒⒓⒔⒕⒖⒗⒘⒙⒚⒛⑴⑵⑶⑷⑸⑹⑺⑻⑼⑽⑾⑿⒀⒁⒂⒃⒄⒅⒆⒇㈠㈡㈢㈣㈤㈥㈦㈧㈨㈩➊➋➌➍➎➏➐➑➒➓㊀㊁㊂㊃㊄㊅㊆㊇㊈㊉ⅰⅱⅲⅳⅴⅵⅶⅷⅸⅹⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ"
        .chars
      
    // 拼音
    case .pinyin:
      return "āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜüńň".chars
      
    // 注音
    case .bopomofo:
      return "ㄚㄛㄜㄧㄨㄩㄝㄞㄟㄠㄡㄢㄣㄤㄥㄦㄅㄆㄇㄈㄉㄊㄋㄌㄍㄎㄏㄐㄑㄒㄓㄔㄕㄖㄗㄘㄙ"
        .chars
      
    // 部首
    case .radical:
      return "丨亅丿乛一乙乚丶八勹匕冫卜厂刀刂儿二匚阝丷几卩冂力冖凵人亻入十厶亠匸讠廴又艹屮彳巛川辶寸大飞干工弓廾广己彐彑巾口马门宀女犭山彡尸饣士扌氵纟巳土囗兀夕小忄幺弋尢夂子贝比灬长车歹斗厄方风父戈卝户火旡见斤耂毛木肀牛牜爿片攴攵气欠犬日氏礻手殳水瓦尣王韦文毋心牙爻曰月爫支止爪白癶歺甘瓜禾钅立龙矛皿母目疒鸟皮生石矢示罒田玄穴疋业衤用玉耒艸臣虫而耳缶艮虍臼米齐肉色舌覀页先行血羊聿至舟衣竹自羽糸糹貝采镸車辰赤辵豆谷見角克里卤麦身豕辛言邑酉豸走足青靑雨齿長非阜金釒隶門靣飠鱼隹風革骨鬼韭面首韋香頁音髟鬯鬥高鬲馬黄鹵鹿麻麥鳥魚鼎黑黽黍黹鼓鼠鼻齊齒龍龠"
        .chars
      
    // 希腊
    case .grease: return "ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩαβγδεζηθικλμνξοπρστυφχψω"
      .chars
      
    // 俄语
    case .rusa: return "АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдеёжзийклмнопрстуфхцчшщъыьэюя"
      .chars
      
    // 拉丁
    case .lation: return "ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞŠŸŒàáâãäåæçèéêëìíîïðñòóõôöøùúûüýþšÿœ"
      .chars
      
    // 韩文
    case .korea: return "dㅏㅑㅓㅕㅗㅛㅜㅠㅡㅣㅐㅒㅔㅖㅘㅙㅚㅝㅞㅟㅢㄱㄴㄷㄹㅁㅂㅅㅇㅈㅊㅋㅌㅍㅎㄲㄸㅚㅆㅉ㉠㉡㉢㉣㉤㉥㉦㉧㉨㉩㉪㉫㉬㉭㉮㉯㉰㉱㉲㉳㉴㉵㉶㉷㉸㉹㉺㉻㈀㈁㈂㈃㈄㈅㈆㈇㈈㈉㈊㈋㈌㈍㈎㈏㈐㈑㈒㈓㈔㈕㈖㈗㈘㈙㈚㈛"
      .chars
      
    // 音标
    case .phonetic:
      return ["a:", "ɔ:", "ɜː", "i:", "u:", "ʌ", "ɒ", "ə", "ɪ", "ʊ", "e", "æ", "eɪ", "aɪ", "ɔɪ", "ɪə", "eə", "ʊə", "əʊ", "aʊ", "p", "t", "k", "f", "θ", "s", "b", "d", "g", "v", "ð", "z", "ʃ", "h", "ts", "tʃ", "j", "tr", "ʒ", "r", "dz", "dʒ", "dr", "w", "m", "n", "ŋ", "l"]
      
    // 颜文字
    // 来源：https://home.gamer.com.tw/creationDetail.php?sn=3123042
//    case .kaomoji:
//      return ["(*ﾟ∀ﾟ*)", "(శωశ)", "(*´∀`)~♥", "σ`∀´)σ", "(〃∀〃)", "(^_っ^)", "(｡◕∀◕｡)", "ヽ(✿ﾟ▽ﾟ)ノ", "ε٩(๑> ₃ <)۶з", "(σ′▽‵)′▽‵)σ", "σ ﾟ∀ ﾟ) ﾟ∀ﾟ)σ", "｡:.ﾟヽ(*´∀`)ﾉﾟ.:｡", "(✪ω✪)", "(∂ω∂)", "─=≡Σ((( つ•̀ω•́)つ", "(๑´ڡ`๑)", "(´▽`ʃ♡ƪ)", "(❛◡❛✿)", "(灬ºωº灬)", "(￣▽￣)/", "╰(*°▽°*)╯   ", "(๑•̀ㅂ•́)و✧", "( ^ω^)", "٩(｡・ω・｡)و", "( ～'ω')～", "(๑ơ ₃ ơ)♥", "(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧", "o(☆Ф∇Ф☆)o", "(￫ܫ￩)", "(♥д♥)", "✧◝(⁰▿⁰)◜✧", "(ᗒᗨᗕ)/", "(=´ω`=)", "(｢･ω･)｢", "(*´д`)", "Σ>―(〃°ω°〃)♡→", "(▰˘◡˘▰)", "ヾ(´ε`ヾ)", "(っ●ω●)っ", "◥(ฅº￦ºฅ)◤", "ヽ( ° ▽°)ノ", "(　ﾟ∀ﾟ) ﾉ♡", "✧*｡٩(ˊᗜˋ*)و✧*｡", "⁽⁽◟(∗ ˊωˋ ∗)◞ ⁾⁾", "ヾ(´︶`*)ﾉ♬", "ヾ(*´∀ ˋ*)ﾉ", "(๑•̀ω•́)ノ", "ヾ (o ° ω ° O ) ノ゙  ", "╮(╯_╰)╭", "(๑•́ ₃ •̀๑)", "(´･_･`)", "(ㆆᴗㆆ)", "┐(´д`)┌", "( ˘･з･)", "( ´•︵•` )", "(｡ŏ_ŏ)", "(◞‸◟)", "( ˘•ω•˘ )", "(눈‸눈)", "(´･ω･`)", "(*´艸`*)", "(〃∀〃)", "(つд⊂)", "(๑´ㅂ`๑)", "ε٩(๑> ₃ <)۶з", "(๑´ڡ`๑)", "(灬ºωº灬)", "(๑• . •๑)", "(๑ơ ₃ ơ)♥", "(●｀ 艸´)", ",,Ծ‸Ծ,,", "(〃ﾟдﾟ〃)", "(๑´ㅁ`)", "(๑¯∀¯๑)", "(〃´∀｀)", "(⋟﹏⋞)", "(ノдT)", "(;´༎ຶД༎ຶ`)", "：ﾟ(｡ﾉω＼｡)ﾟ･｡", "(TдT)", "(☍﹏⁰)", "(╥﹏╥)", "｡ﾟ(ﾟ´ω`ﾟ)ﾟ｡", "இдஇ", "｡ﾟヽ(ﾟ´Д`)ﾉﾟ｡", "。･ﾟ･(つд`ﾟ)･ﾟ･", "・゜・(PД`q｡)・゜・", "(ﾟд⊙)", "(‘⊙д-)", "Σ(*ﾟдﾟﾉ)ﾉ", "(((ﾟДﾟ;)))", "(((ﾟдﾟ)))", "(☉д⊙)", "(|||ﾟдﾟ)", "(´⊙ω⊙`)", "ฅ(๑*д*๑)ฅ!!", "(゜ロ゜)", "(✘﹏✘ა)", "(✘Д✘๑ )", "(╬☉д⊙)", "(／‵Д′)／~ ╧╧", "(╯‵□′)╯︵┴─┴", "(◓Д◒)✄╰⋃╯", "(ﾒﾟДﾟ)ﾒ", "(`へ´≠)", "(#ﾟ⊿`)凸", "(╬▼дﾟ)", "(ᗒᗣᗕ)՞", "( ิ◕㉨◕ ิ)", "(❍ᴥ❍ʋ)", "(◕ܫ◕)", "(ΦωΦ)", "ก็ʕ•͡ᴥ•ʔ ก้", "(=´ω`=)", "(⁰⊖⁰)", "(=´ᴥ`)", "ฅ●ω●ฅ", "(っ^_^)っ", "（¯﹃¯）", "m(__)m", "( ͡ ͡° ͜ ʖ ͡ ͡°)", "( ° ͜ʖ͡°)╭∩╮", "(⌐▀͡ ̯ʖ▀)"]
      
    // 假名
    case .jp:
      return "あいうえおかがきぎくぐけげこごさざしじすずせぜそぞただちぢつづてでとどなにぬねのはばぱひびぴふぶぷへべぺほぼぽまみむめもゃやゅゆょよらりるれろわをんアィイウェエオカガキギクグケゲコゴサザシジスズセゼソゾタダチヂツヅテデトドナニヌネノハバパヒビピフブプヘベペホボポマミムメモャヤュユョヨラリルレロワヲン"
        .chars
    }
  }
}
