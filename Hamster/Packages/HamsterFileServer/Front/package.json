{"name": "filebrowser-frontend", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "vite dev", "serve": "vite serve", "build": "vite build", "watch": "vite build --watch", "clean": "find ./dist -maxdepth 1 -mindepth 1 ! -name '.gitkeep' -exec rm -r {} +", "lint": "eslint --ext .vue,.js src/", "lint:fix": "eslint --ext .vue,.js --fix src/", "format": "prettier --write ."}, "dependencies": {"ace-builds": "^1.23.4", "clipboard": "^2.0.11", "core-js": "^3.32.0", "css-vars-ponyfill": "^2.4.8", "filesize": "^10.0.8", "js-base64": "^3.7.5", "lodash.clonedeep": "^4.5.0", "lodash.throttle": "^4.1.1", "material-icons": "^1.13.9", "moment": "^2.29.4", "normalize.css": "^8.0.1", "noty": "^3.2.0-beta", "pretty-bytes": "^6.1.1", "qrcode.vue": "^1.7.0", "tus-js-client": "^3.1.1", "utif": "^3.1.0", "vue": "^2.7.14", "vue-async-computed": "^3.9.0", "vue-i18n": "^8.28.2", "vue-lazyload": "^1.3.5", "vue-router": "^3.6.5", "vue-simple-progress": "^1.1.1", "vuex": "^3.6.2", "vuex-router-sync": "^5.0.0", "whatwg-fetch": "^3.6.17"}, "devDependencies": {"@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue2": "^2.2.0", "@vue/eslint-config-prettier": "^8.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.46.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.16.1", "jsdom": "^22.1.0", "postcss": "^8.4.31", "prettier": "^3.0.1", "terser": "^5.19.2", "vite": "^4.4.9", "vite-plugin-compression2": "^0.10.3", "vite-plugin-rewrite-all": "^1.0.1"}, "browserslist": ["> 1%", "last 2 versions", "not ie < 11"]}