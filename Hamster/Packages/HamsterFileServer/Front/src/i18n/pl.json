{"buttons": {"cancel": "<PERSON><PERSON><PERSON>", "close": "Zamknij", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copyFile": "<PERSON><PERSON><PERSON><PERSON> plik", "copyToClipboard": "kopiuj do schowka", "create": "Utwórz", "delete": "Usuń", "download": "<PERSON><PERSON><PERSON>", "hideDotfiles": "", "info": "Informacja", "more": "<PERSON><PERSON><PERSON><PERSON>", "move": "P<PERSON><PERSON><PERSON>ś", "moveFile": "Przenieś plik", "new": "Nowy", "next": "Następny", "ok": "OK", "permalink": "Uzyskaj link bezpośredni (permalink)", "previous": "Poprzedni", "publish": "Opublikuj", "rename": "Zmień nazwę", "replace": "Zamień", "reportIssue": "<PERSON><PERSON>ł<PERSON>ś problem", "save": "<PERSON><PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON>", "search": "Szukaj", "select": "<PERSON><PERSON><PERSON><PERSON>", "selectMultiple": "Zaznacz wiele", "share": "Udostępnij", "shell": "Pokaż/ukryj powłokę", "switchView": "Zmień widok", "toggleSidebar": "Pokaż/ukryj panel boczny", "update": "Aktualizuj", "upload": "<PERSON><PERSON><PERSON>"}, "download": {"downloadFile": "Pobierz plik", "downloadFolder": "Pobierz folder", "downloadSelected": "Pobierz zaznaczone"}, "errors": {"forbidden": "<PERSON>e posiadasz uprawnień potrzebnych, by <PERSON><PERSON><PERSON><PERSON> do tego dostęp.", "internal": "Pojawił się poważny problem.", "notFound": "Ten adres nie jest poprawny."}, "files": {"body": "Body", "clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "closePreview": "Zamknij poprzednie", "files": "Pliki", "folders": "Foldery", "home": "Katalog domowy", "lastModified": "Ostatnio <PERSON>", "loading": "Ładowanie...", "lonely": "Smutno gdy tak pusto...", "metadata": "Metadane", "multipleSelectionEnabled": "Zaznaczenie wielu włączone", "name": "Nazwa", "size": "Rozmiar", "sortByLastModified": "Sortuj wg. daty m<PERSON>", "sortByName": "Sortuj wg. nazwy", "sortBySize": "Sortuj wg. roz<PERSON>ru"}, "help": {"click": "wy<PERSON>rz plik lub foler", "ctrl": {"click": "wybierz wiele plików lub folderów", "f": "ot<PERSON><PERSON><PERSON> wyszuki<PERSON>ę", "s": "pobierz aktywny plik lub folder"}, "del": "usuń zaznaczone", "doubleClick": "otwórz plik lub folder", "esc": "wy<PERSON><PERSON>ść zaznaczenie i/lub zamknij okno z powiadomieniem", "f1": "ta informacja", "f2": "zmień nazwę pliku", "help": "Pomoc"}, "languages": {"he": "עברית", "hu": "<PERSON><PERSON><PERSON>", "ar": "العربية", "de": "De<PERSON>ch", "en": "English", "es": "Español", "fr": "Français", "is": "Íslenska", "it": "Italiano", "ja": "日本語", "ko": "한국어", "nlBE": "Nederlands (België)", "pl": "<PERSON><PERSON>", "pt": "Português", "ptBR": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "ro": "Română", "ru": "Русский", "sk": "Slovenčina", "svSE": "Svenska (Sverige)", "tr": "Türkçe", "ua": "Українська", "zhCN": "中文 (简体)", "zhTW": "中文 (繁體)"}, "login": {"createAnAccount": "Utwórz konto", "loginInstead": "Takie konto już istnieje", "password": "<PERSON><PERSON><PERSON>", "passwordConfirm": "Potwierd<PERSON><PERSON> hasła", "passwordsDontMatch": "Hasła różnią się", "signup": "Rejestracja", "submit": "Logowanie", "username": "Nazwa użytkownika", "usernameTaken": "Nazwa użytkownika już zajęta", "wrongCredentials": "Błędne dane logowania"}, "permanent": "Permanentny", "prompts": {"copy": "<PERSON><PERSON><PERSON><PERSON>", "copyMessage": "<PERSON><PERSON><PERSON>rz lokalizację do której mają być skopiowane wybrane pliki", "currentlyNavigating": "Obecnie przeglądasz:", "deleteMessageMultiple": "<PERSON><PERSON> j<PERSON> pewien że ch<PERSON> {count} plik(ów)?", "deleteMessageSingle": "<PERSON><PERSON>, że ch<PERSON> usun<PERSON> ten plik/folder?", "deleteTitle": "Us<PERSON>ń pliki", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "download": "Pobierz pliki", "downloadMessage": "Wybierz format, jaki chesz pob<PERSON>.", "error": "Pojawił się nieznany błąd", "fileInfo": "Informacje o pliku", "filesSelected": "{count} plików zostało zaznaczonych.", "lastModified": "<PERSON><PERSON><PERSON>", "move": "P<PERSON><PERSON><PERSON>ś", "moveMessage": "<PERSON><PERSON><PERSON><PERSON> nową lokalizację dla swoich plik(ów)/folder(ów):", "newArchetype": "Utwórz nowy wpis na bazie wybranego wzorca. Twój plik będzie utworzony w wybranym folderze.", "newDir": "Nowy folder", "newDirMessage": "Podaj nazwę tworzonego folderu.", "newFile": "Nowy plik", "newFileMessage": "Podaj nazwętworzonego pliku.", "numberDirs": "Ilość katalogów", "numberFiles": "Ilość plików", "rename": "Zmień nazwę", "renameMessage": "Podaj nową nazwę dla", "replace": "Zamień", "replaceMessage": "Jednen z plików który próbujesz wrzucić próbje nadpisać plik o tej samej nazwie. <PERSON>zy chcesz nadpisać poprzedni plik?\n", "schedule": "<PERSON><PERSON>", "scheduleMessage": "<PERSON><PERSON><PERSON><PERSON> datę i czas dla publikacji tego wpisu.", "show": "Po<PERSON><PERSON>", "size": "Rozmiar", "upload": "Prześ<PERSON>j", "uploadMessage": "<PERSON><PERSON><PERSON> wybrać metodę przesyłania"}, "search": {"images": "Zdjęcia", "music": "Muzyka", "pdf": "PDF", "pressToSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON> enter, aby w<PERSON>...", "search": "Szukaj...", "typeToSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby w<PERSON>...", "types": "<PERSON><PERSON>", "video": "Wideo"}, "settings": {"admin": "Admin", "administrator": "Administrator", "allowCommands": "<PERSON><PERSON><PERSON><PERSON>", "allowEdit": "<PERSON><PERSON><PERSON><PERSON>, zmiana nazwy i usuniecie plików lub folderów", "allowNew": "Tworzenie nowych plików lub folderów", "allowPublish": "Tworzenie nowych wpisów i stron", "allowSignup": "Zezwól na rejestrację użytkowników", "avoidChanges": "(pozostaw puste aby nie zosatało zmienione)", "branding": "Branding", "brandingDirectoryPath": "Folder brandingowy", "brandingHelp": "<PERSON><PERSON><PERSON><PERSON> dos<PERSON> wygląd i doznania użytkownika swojej instancji File Browser poprzez zmianę jej nazwy, zmianę logo, dodanie własnych stylów, a nawet wyłączyć linki zewnętrzne do GitHuba.\nW celu pozyskania większej ilości informacji nt. osobistego brandingu, zapoznaj się z {0}.", "changePassword": "<PERSON><PERSON><PERSON>", "commandRunner": "Narzędzie do wykonywania poleceń", "commandRunnerHelp": "<PERSON> moż<PERSON>z ustawi<PERSON> komendy, kt<PERSON>re będą wykonywane przy danych zdarzeniach. Musisz wpisywać po jednej na linjkę. Zmienne środowiskowe {0} i {1} b<PERSON><PERSON><PERSON> dostę<PERSON>, gdzie {0} jest względne wobec {1}. Więcej informacji o tej funkcji i dostępnych zmiennych środowiskowych znajdziesz tutaj: {2}.", "commandsUpdated": "Polecenie zaktualizowane!", "createUserDir": "Automatycznie utwórz katalog domowy użytkownika podczas dodania nowego użytkownika", "customStylesheet": "Własny arkusz stylów", "defaultUserDescription": "<PERSON><PERSON>lne ustawienia dla nowych użytkowników.", "disableExternalLinks": "Wyłącz linki zewnętrzne (z wyjątkiem dokumentacji)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "dokumentacja", "examples": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "executeOnShell": "Wykonaj w powłoce", "executeOnShellDescription": "Domyślnie File Browser wykonuje polecenia wywołując ich pliki binarne bezpośrednio. Je<PERSON><PERSON> preferujesz wykonywanie ich w powłoce (jak np. Bash czy PowerShell), m<PERSON><PERSON><PERSON><PERSON><PERSON> to tutaj wraz z wymaganymi flagami i argumentami. <PERSON><PERSON><PERSON> to ustawienie jest aktywne, polecenie które wykonarz zostanie dodane jako argument. <PERSON><PERSON><PERSON><PERSON> się to zarówno do poleceń użytkownika jak i zaczepów zdarzeń.", "globalRules": "To jest globalne zestawienie reguł zezwalających i zabraniających. Stosują się one do każdego użytkownika. Możesz zdefiniować indywidualne zasady w ustawieniach każdego użytkownika, by <PERSON><PERSON><PERSON><PERSON><PERSON> te reguły.", "globalSettings": "Ustawienia Globalne", "hideDotfiles": "Ukryj ukryte pliki", "insertPath": "Wstaw ścieżkę", "insertRegex": "Wstaw wyrażenie regularne", "instanceName": "Nazwa instancji", "language": "Język", "lockPassword": "Zablokuj użytkownikowi możliwość zmiany hasła", "newPassword": "<PERSON>je nowe hasło", "newPasswordConfirm": "Potwierdź swoje hasło", "newUser": "Nowy Użytkownik", "password": "<PERSON><PERSON><PERSON>", "passwordUpdated": "Hasło zostało zapisane!", "path": "Ścieżka", "perm": {"create": "Tworzenie plików i katalogów", "delete": "Usuwanie plików i katalogów", "download": "Pobieranie", "execute": "Wykonywanie poleceń", "modify": "Edycja plików", "rename": "Zmiana nazw lub przenoszenie plików i katalogów", "share": "Udostępnianie plików"}, "permissions": "Uprawnienia", "permissionsHelp": "<PERSON><PERSON><PERSON><PERSON> <PERSON> użytkownika administratorem, lub w<PERSON><PERSON><PERSON> uprawnienia indywidualnie. <PERSON><PERSON><PERSON> opcj<PERSON> \"Administrator\", wszystkie pozostałe opcje zostaną automatycznie zaznaczone. Zarządzanie użytkownikami pozostaje przywilejem administratora.\n", "profileSettings": "T<PERSON><PERSON>j profil", "ruleExample1": "uniemożliwia dostęp do któregokolwiek z ukrytych plików (takich jak .git, .gitignore) w każdym folderze.\n", "ruleExample2": "blokuje dostęp do pliku Caddyfile w głównym katalogu zakresu.", "rules": "Uprawnienia", "rulesHelp": "Tu możesz zdefiniować zestawienie reguł zezwalających i zabraniających dla tego konkretnego użytkownika. Zablokowane pliki nie będą widoczne na listach i nie będą dostępne dla użytkownika. Wspierane są wyrażenia regularne i ścieżki względne wobec zakresu użytkownika.\n", "scope": "<PERSON><PERSON><PERSON>", "settingsUpdated": "Uprawnienia Zapisane!", "shareDuration": "Okres udostępniania", "shareManagement": "Zarządzanie udostępnianiem", "singleClick": "Pojedyncze kliknięcie", "themes": {"dark": "ciemny", "light": "jasny", "title": "<PERSON><PERSON><PERSON>"}, "user": "Użytkownik", "userCommands": "Polecenia", "userCommandsHelp": "Lista oddzielonych spacjami poleceń dostępnych dla tego użytkownika. Przykład:\n", "userCreated": "Użytkownik zapisany!", "userDefaults": "Domyślne ustawienia użytkownika", "userDeleted": "Użytkownik usunięty!", "userManagement": "Zarządzanie użytkownikami", "userUpdated": "Użytkownik zapisany!", "username": "Nazwa użytkownika", "users": "Użytkownicy"}, "sidebar": {"help": "Pomoc", "hugoNew": "<PERSON>", "login": "<PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myFiles": "<PERSON><PERSON>", "newFile": "Nowy plik", "newFolder": "Nowy folder", "preview": "Podgląd", "settings": "Ustawienia", "signup": "Rejestracja", "siteSettings": "Ustawienia Strony"}, "success": {"linkCopied": "Link <PERSON>!"}, "time": {"days": "Dni", "hours": "<PERSON><PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "seconds": "Sekundy", "unit": "Jednostka czasu"}}