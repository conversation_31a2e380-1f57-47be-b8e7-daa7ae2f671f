{"buttons": {"cancel": "Abbrechen", "close": "Schließen", "copy": "<PERSON><PERSON><PERSON>", "copyFile": "<PERSON><PERSON><PERSON>", "copyToClipboard": "In Zwischenablage kopieren", "create": "<PERSON>eu", "delete": "Löschen", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "<PERSON><PERSON>", "folder": "<PERSON><PERSON><PERSON>", "hideDotfiles": "Versteckte Dateien ausblenden", "info": "Info", "more": "mehr", "move": "Verschieben", "moveFile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "new": "<PERSON>eu", "next": "nächste", "ok": "OK", "permalink": "permanenten Verweis anzeigen", "previous": "vorherige", "publish": "Veröffentlichen", "rename": "umbenennen", "replace": "<PERSON><PERSON><PERSON><PERSON>", "reportIssue": "<PERSON><PERSON> melden", "save": "Speichern", "schedule": "Planung", "search": "<PERSON><PERSON>", "select": "Auswählen", "selectMultiple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "share": "Teilen", "shell": "Kommandozeile ein/ausschalten", "submit": "<PERSON><PERSON><PERSON><PERSON>", "switchView": "Ansicht wechseln", "toggleSidebar": "Seitenleiste anzeigen", "update": "Update", "upload": "Upload", "openFile": "<PERSON><PERSON>", "continue": "Fortfahren"}, "download": {"downloadFile": "Download Datei", "downloadFolder": "Download Ordner", "downloadSelected": "Auswahl herunterladen"}, "errors": {"forbidden": "Sie haben keine Berechtigung dies abzurufen.", "internal": "Etwas ist schief gelaufen.", "notFound": "<PERSON>ser Ort kann nicht angezeigt werden.", "connection": "Der Server ist nicht erreichbar."}, "files": {"body": "Body", "clear": "Schließen", "closePreview": "Vorschau schließen", "files": "<PERSON><PERSON>", "folders": "<PERSON><PERSON><PERSON>", "home": "Home", "lastModified": "zu<PERSON>zt verändert", "loading": "Lade...", "lonely": "Hier scheint nichts zu sein...", "metadata": "<PERSON><PERSON><PERSON>", "multipleSelectionEnabled": "Mehrfachauswahl aktiviert", "name": "Name", "size": "Größe", "sortByLastModified": "Nach Änderungsdatum sortieren", "sortByName": "Nach Namen sortieren", "sortBySize": "Nach Größe sortieren", "noPreview": "<PERSON><PERSON><PERSON> diese <PERSON> ist keine Vorschau verfügbar."}, "help": {"click": "Wähle Datei oder Ordner", "ctrl": {"click": "Markiere mehrere Dateien oder Ordner", "f": "<PERSON><PERSON><PERSON> eine neue Suche", "s": "Speichert eine Datei oder einen Ordner am akutellen Ort"}, "del": "Löscht die ausgewählten Elemente", "doubleClick": "<PERSON><PERSON><PERSON> eine Datei oder einen Ordner", "esc": "Auswahl zurücksetzen und/oder Dialog schließen", "f1": "Diese Informationsseite", "f2": "<PERSON><PERSON> umben<PERSON>n", "help": "<PERSON><PERSON><PERSON>"}, "languages": {"he": "עברית", "hu": "<PERSON><PERSON><PERSON>", "ar": "العربية", "de": "De<PERSON>ch", "en": "English", "es": "Español", "fr": "Français", "is": "Icelandic", "it": "Italiano", "ja": "日本語", "ko": "한국어", "nlBE": "Dutch (Belgium)", "pl": "<PERSON><PERSON>", "pt": "Português", "ptBR": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "ro": "Romanian", "ru": "Русский", "sk": "Slovenčina", "svSE": "Swedish (Sweden)", "tr": "Türkçe", "ua": "Українська", "zhCN": "中文 (简体)", "zhTW": "中文 (繁體)"}, "login": {"createAnAccount": "Account erstellen", "loginInstead": "Account besteht bereits", "password": "Passwort", "passwordConfirm": "Passwort Bestätigung", "passwordsDontMatch": "Passwörter stimmen nicht überein", "signup": "Registrieren", "submit": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "usernameTaken": "Benutzername ist bereits vergeben", "wrongCredentials": "Falsche Zugangsdaten"}, "permanent": "Permanent", "prompts": {"copy": "<PERSON><PERSON><PERSON>", "copyMessage": "Wählen Sie einen Zielort zum Kopieren:", "currentlyNavigating": "Aktueller Ort:", "deleteMessageMultiple": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass <PERSON> {count} Datei(en) löschen möchten?", "deleteMessageSingle": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> diesen Ordner/diese Datei löschen möchten?", "deleteMessageShare": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass Sie diese Freigabe löschen möchten ({path})?", "deleteTitle": "Lösche <PERSON>", "displayName": "Anzeigename:", "download": "Lade <PERSON>", "downloadMessage": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Format zum Herunterladen aus.", "error": "Etwas ist schief gelaufen", "fileInfo": "Dateiinformation", "filesSelected": "{count} <PERSON><PERSON> aus<PERSON>w<PERSON>.", "lastModified": "Zuletzt geändert", "move": "Verschieben", "moveMessage": "Wählen Sie einen neuen Platz für ihre Datei(en)/Ordner:", "newArchetype": "<PERSON><PERSON><PERSON> neuen Beitrag auf dem Archetyp. Ihre Datei wird im Inhalteordner erstellt.", "newDir": "<PERSON><PERSON><PERSON> Ordner", "newDirMessage": "Geben Sie den Namen des neuen Ordners an.", "newFile": "Neue Datei", "newFileMessage": "Geb<PERSON> Sie den Namen der neuen Datei an.", "numberDirs": "Anzahl der Ordner", "numberFiles": "<PERSON><PERSON><PERSON> der Dateien", "rename": "Umbenennen", "renameMessage": "<PERSON>ügen Si<PERSON> einen Namen ein für", "replace": "<PERSON><PERSON><PERSON><PERSON>", "replaceMessage": "Eine der Datei mit dem gleichen Namen, wie die Si<PERSON> hochladen wollen, existiert bereits. Soll die vorhandene Datei übersprungen oder ersetzt werden?\n", "schedule": "Plan", "scheduleMessage": "<PERSON>ählen Sie ein Datum und eine Zeit für die Veröffentlichung dieses Beitrags.", "show": "Anzeigen", "size": "Größe", "upload": "Upload", "uploadFiles": "Upload von {files} Dateien...", "uploadMessage": "Wählen Sie eine Upload-Methode", "optionalPassword": "Optionales Passwort"}, "search": {"images": "Bilder", "music": "Mu<PERSON>", "pdf": "PDF", "pressToSearch": "Drücken Sie Enter um zu suchen...", "search": "Suche...", "typeToSearch": "Tippen um zu suchen...", "types": "Typen", "video": "Video"}, "settings": {"admin": "Admin", "administrator": "Administrator", "allowCommands": "Befehle ausführen", "allowEdit": "Bearbei<PERSON>, Umbenennen und Löschen von Dateien oder Ordnern", "allowNew": "<PERSON><PERSON><PERSON><PERSON> neuer Dateien und Ordner", "allowPublish": "Veröffentlichen von neuen Beiträgen und Seiten", "allowSignup": "Erlaube Benutzern sich zu registrieren", "avoidChanges": "(leer lassen um Änderungen zu vermeiden)", "branding": "Design", "brandingDirectoryPath": "Designverzeichnispfad", "brandingHelp": "Sie können das Erscheinungsbild Ihres File Browser anpassen, in dem Si<PERSON> den Namen ändern, das Logo austauchsen oder eigene Stile definieren und sogar externe Links zu GitHub deaktivieren.\nUm mehr Informationen zum Anpassen des Designs zu bekommen, gehen Sie bitte zu {0}.", "changePassword": "Passwort ändern", "commandRunner": "Befehlseingabe", "commandRunnerHelp": "Hier könne Sie Befehle eintragen, welche bei den benannten Aktionen ausgeführt werden. Sie müssen pro Zeile jeweils einen Befehl eingeben. Die Umgebungsvariable {0} und {1} sind verfügbar, wobei {0} relative zu {1} ist. Für mehr Informationen über diese Funktion und die verfügbaren Umgebungsvariablen, lesen Si<PERSON> bitte die {2}.", "commandsUpdated": "Befehle aktualisiert!", "createUserDir": "Automatisches Erstellen des Home-Verzeichnisses beim Anlegen neuer Benutzer", "tusUploads": "Gestückelter Upload", "tusUploadsHelp": "File Browser unterstützt das Hochladen von gestückelten Dateien und ermöglicht so einen effizienten, zuverlässigen, fortsetzbaren und gestückelten Datei-Upload auch in unzuverlässigen Netzwerken.", "tusUploadsChunkSize": "Gibt die maximale Größe pro Anfrage an (direkte Uploads werden für kleinere Uploads verwendet). Bitte geben Sie eine Byte-Angabe oder eine Zeichenfolge wie 10 MB, 1 GB usw. an", "tusUploadsRetryCount": "<PERSON><PERSON><PERSON> der Wiederholungsversuche, wenn das Hochladen eines Stückes fehlschlägt.", "customStylesheet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultUserDescription": "Das sind die Standardeinstellung für Benutzer", "disableExternalLinks": "Externe Links deaktivieren (außer Dokumentation)", "disableUsedDiskPercentage": "Diagramm zur Festplattennutzung deaktivieren", "documentation": "Dokumentation", "examples": "<PERSON><PERSON><PERSON><PERSON>", "executeOnShell": "In Shell ausführen", "executeOnShellDescription": "Es ist voreingestellt das der File Brower Befehle ausführt in dem er die Befehlsdateien direkt aufruft. Wen<PERSON> <PERSON><PERSON> woll<PERSON>, dass sie über einer Kommandozeile (wie Bash oder PowerShell) laufen, könne Sie diese hier definieren mit allen bennötigten Argumenten und Optionen. <PERSON><PERSON> g<PERSON>, wird das Kommando das ausgeführt werden soll als Parameter angehängt. Das gilt für Benuzerkommandos sowie auch für Ereignisse.", "globalRules": "Das ist ein globales Set von Regeln die erlauben oder nicht erlauben. Die sind für alle Benutzer zutreffend. Es können spezielle Regeln in den Einstellungen der Benutzer definiert werden, die diese überschreiben.", "globalSettings": "Globale Einstellungen", "hideDotfiles": "Versteckte Dateien ausblenden", "insertPath": "Pfad einfügen", "insertRegex": "Regulären <PERSON>druck (Regex) einfügen", "instanceName": "Instanzname", "language": "<PERSON><PERSON><PERSON>", "lockPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>, dass der Benutzer sein Passwort ändert", "newPassword": "Ihr neues Passwort.", "newPasswordConfirm": "Bestätigen Sie Ihr neues Passwort", "newUser": "<PERSON><PERSON><PERSON>", "password": "Passwort", "passwordUpdated": "Passwort aktualisiert!", "path": "Pfad", "perm": {"create": "Dateien und Ordner erstellen", "delete": "Dateien und Ordner löschen", "download": "Download", "execute": "Befehle ausführen", "modify": "<PERSON><PERSON> editieren", "rename": "Umbenennen oder Verschieben von Dateien oder Ordnern", "share": "<PERSON><PERSON> teilen"}, "permissions": "Berechtigungen", "permissionsHelp": "<PERSON>e können einem Benutzer Administratorrechte einräumen oder die Berechtigunen individuell festlegen.  <PERSON><PERSON> <PERSON><PERSON> \"Administrator\" ausw<PERSON>hlen, werden alle anderen Rechte automatisch vergeben. Die Nutzerverwaltung kann nur durch einen Administrator erfolgen.\n", "profileSettings": "Profileinstellungen", "ruleExample1": "Verhindert den Zugang zu versteckten Dateien (dot-Files, wie .git, .gitignore) in allen Ordnern\n", "ruleExample2": "blockiert den Zugang auf Dateien mit dem Namen Caddyfile in der Wurzel/Basis des Scopes.", "rules": "Regeln", "rulesHelp": "Hier können Sie erlaubte und verbotene Aktionen für einen einzelnen Benutzer festlegen. Blockierte Dateien werden nicht im Listing angezeigt und sind nicht erreichbar für den Nutzer. Wir unterstützen reguläre Ausdrücke (Regex) und Pfade die relativ zum Benutzerordner sind. \n", "scope": "<PERSON><PERSON>", "setDateFormat": "Exaktes Datumsformat setzen", "settingsUpdated": "Einstellungen aktualisiert!", "shareDuration": "<PERSON><PERSON>", "shareManagement": "Freigaben verwalten", "shareDeleted": "Freigabe gelöscht!", "singleClick": "Einfacher Klick zum Öffnen von Dateien und Ordnern", "themes": {"dark": "<PERSON><PERSON><PERSON>", "light": "Hell", "title": "Erscheinungsbild"}, "user": "<PERSON><PERSON><PERSON>", "userCommands": "<PERSON><PERSON><PERSON><PERSON>", "userCommandsHelp": "<PERSON><PERSON>, mit einem Leerzeichen als Trennung, mit den für diesen Nutzer verfügbaren Befehlen. Beispiel:\n", "userCreated": "<PERSON>utzer angelegt!", "userDefaults": "Benutzer Standard Einstellungen", "userDeleted": "Benutzer gelöscht!", "userManagement": "Benutzerverwaltung", "userUpdated": "Benutzer aktualisiert!", "username": "<PERSON><PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON>"}, "sidebar": {"help": "<PERSON><PERSON><PERSON>", "hugoNew": "<PERSON>", "login": "Anmelden", "logout": "Abmelden", "myFiles": "<PERSON><PERSON>", "newFile": "Neue Datei", "newFolder": "<PERSON><PERSON><PERSON> Ordner", "preview": "Vorschau", "settings": "Einstellungen", "signup": "Registrieren", "siteSettings": "Seiteneinstellungen"}, "success": {"linkCopied": "Verweis wurde kopiert!"}, "time": {"days": "Tage", "hours": "Stunden", "minutes": "Minuten", "seconds": "Sekunden", "unit": "Zeiteinheit"}}