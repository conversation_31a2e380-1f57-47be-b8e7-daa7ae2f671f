{"buttons": {"cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "copyFile": "<PERSON><PERSON><PERSON> le <PERSON>", "copyToClipboard": "Copier dans le presse-papier", "create": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "download": "Télécharger", "file": "<PERSON><PERSON><PERSON>", "folder": "Dossier", "hideDotfiles": "Masquer les dotfiles", "info": "Info", "more": "Plus", "move": "<PERSON><PERSON><PERSON><PERSON>", "moveFile": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "new": "Nouveau", "next": "Suivant", "ok": "OK", "permalink": "Obtenir un lien permanent", "previous": "Précédent", "publish": "Publier", "rename": "<PERSON>mmer", "replace": "<PERSON><PERSON>lace<PERSON>", "reportIssue": "Rapport d'erreur", "save": "Enregistrer", "schedule": "Fixer la date", "search": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectMultiple": "Sélection multiple", "share": "Partager", "shell": "Activer/Dés<PERSON>r le shell", "submit": "Envoyer", "switchView": "Changer le mode d'affichage", "toggleSidebar": "Afficher/Masquer la barre latérale", "update": "Mettre à jour", "upload": "Importer", "openFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> le <PERSON>er"}, "download": {"downloadFile": "Télécharger le fichier", "downloadFolder": "Télécharger le dossier", "downloadSelected": "Télécharger la selection"}, "errors": {"forbidden": "Vous n'avez pas la permission d'accéder à cela.", "internal": "Aïe ! Quelque chose s'est mal passé.", "notFound": "Impossible d'accéder à cet emplacement.", "connection": "Le serveur n'est pas accessible."}, "files": {"body": "Corps", "clear": "<PERSON><PERSON><PERSON>", "closePreview": "Fermer la prévisualisation", "files": "Fichiers", "folders": "Dossiers", "home": "Accueil", "lastModified": "Dernière modification", "loading": "Chargement...", "lonely": "Il semble qu'il n'y ait rien par ici...", "metadata": "Metadonnées", "multipleSelectionEnabled": "Sélection multiple activée", "name": "Nom", "size": "<PERSON><PERSON>", "sortByLastModified": "Trier par date de dernière modification", "sortByName": "Trier par nom", "sortBySize": "Trier par taille", "noPreview": "Il n'y a pas de prévisualisation pour ce fichier."}, "help": {"click": "Sélectionner un élément", "ctrl": {"click": "Sélectionner plusieurs éléments", "f": "Ouvrir l'invité de recherche", "s": "Télécharger l'élément actuel"}, "del": "Supprimer les éléments sélectionnés", "doubleClick": "<PERSON><PERSON><PERSON><PERSON><PERSON> un élément", "esc": "Désélectionner et/ou fermer la boîte de dialogue", "f1": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'aide", "f2": "<PERSON><PERSON> le <PERSON>", "help": "Aide"}, "languages": {"he": "עברית", "hu": "<PERSON><PERSON><PERSON>", "ar": "العربية", "de": "De<PERSON>ch", "en": "English", "es": "Español", "fr": "Français", "is": "", "it": "Italiano", "ja": "日本語", "ko": "한국어", "nlBE": "", "pl": "<PERSON><PERSON>", "pt": "Português", "ptBR": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "ro": "", "ru": "Русский", "sk": "Slovenčina", "svSE": "", "tr": "Türkçe", "ua": "Українська", "zhCN": "中文 (简体)", "zhTW": "中文 (繁體)"}, "login": {"createAnAccount": "<PERSON><PERSON><PERSON> un compte", "loginInstead": "Vous avez déjà un compte", "password": "Mot de passe", "passwordConfirm": "Confirmation de mot de passe", "passwordsDontMatch": "Les mots de passe ne concordent pas", "signup": "S'inscrire", "submit": "Se connecter", "username": "Utilisa<PERSON>ur", "usernameTaken": "Le nom d'utilisateur est déjà pris", "wrongCredentials": "Identifiants incorrects !"}, "permanent": "Permanent", "prompts": {"copy": "<PERSON><PERSON><PERSON>", "copyMessage": "Choisissez l'emplacement où copier la sélection :", "currentlyNavigating": "Dossier courant :", "deleteMessageMultiple": "Êtes-vous sûr de vouloir supprimer ces {count} élément(s) ?", "deleteMessageSingle": "Êtes-vous sûr de vouloir supprimer cet élément ?", "deleteMessageShare": "Êtes-vous sûr de vouloir supprimer ce partage ({path}) ?", "deleteTitle": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "Nom :", "download": "Télécharger", "downloadMessage": "Choisissez le format de téléchargement :", "error": "<PERSON><PERSON><PERSON> chose s'est mal passé", "fileInfo": "Informations", "filesSelected": "{count} éléments sélectionnés", "lastModified": "Dernière modification", "move": "<PERSON><PERSON><PERSON><PERSON>", "moveMessage": "Choisissez l'emplacement où déplacer la sélection :", "newArchetype": "Créer un nouveau post basé sur un archétype. Votre fichier sera créé dans le dossier de contenu.", "newDir": "Nouveau dossier", "newDirMessage": "Nom du nouveau dossier :", "newFile": "Nouveau fichier", "newFileMessage": "Nom du nouveau fichier :", "numberDirs": "Nombre de dossiers", "numberFiles": "Nombre de fichiers", "rename": "<PERSON>mmer", "renameMessage": "Nouveau nom pour", "replace": "<PERSON><PERSON>lace<PERSON>", "replaceMessage": "Un des fichiers que vous êtes en train d'importer a le même nom qu'un autre déjà présent. Voulez-vous remplacer le fichier actuel par le nouveau ?\n", "schedule": "Fixer la date", "scheduleMessage": "Choisissez une date pour planifier la publication de ce post", "show": "<PERSON><PERSON>", "size": "<PERSON><PERSON>", "upload": "Importer", "uploadFiles": "Importation de {files} fichiers...", "uploadMessage": "Séléctionnez une option d'import.", "optionalPassword": "Mot de passe optionnel"}, "search": {"images": "Images", "music": "Musique", "pdf": "PDF", "pressToSearch": "Appuyez du entrée pour chercher...", "search": "Recherche en cours...", "typeToSearch": "Écrivez pour chercher...", "types": "Types", "video": "Video"}, "settings": {"admin": "Admin", "administrator": "Administrateur", "allowCommands": "Exécuter des commandes", "allowEdit": "Editer, renommer et supprimer des fichiers ou des dossiers", "allowNew": "<PERSON><PERSON>er de nouveaux fichiers et dossiers", "allowPublish": "Publier de nouveaux posts et pages", "allowSignup": "Autoriser les utilisateurs à s'inscrire", "avoidChanges": "(Laisser vide pour conserver l'actuel)", "branding": "Image de marque", "brandingDirectoryPath": "Chemin du dossier d'image de marque", "brandingHelp": "Vous pouvez personnaliser l'apparence de votre instance de File Browser en changeant son nom, en remplaçant le logo, en ajoutant des styles personnalisés et même en désactivant les liens externes vers GitHub.\nPour plus d'informations sur la personnalisation de l'image de marque, veuillez consulter la {0}.", "changePassword": "Modifier le mot de passe", "commandRunner": "Command runner", "commandRunnerHelp": "<PERSON><PERSON>, vous pouvez définir les commandes qui sont exécutées pour les événements nommés précédemments. Vous devez en écrire une par ligne. Les variables d'environnement {0} et {1} seront disponibles, {0} étant relatif à {1}. Pour plus d'informations sur cette fonctionnalité et les variables d'environnement disponibles, veuillez lire la {2}.", "commandsUpdated": "Commandes mises à jour !", "createUserDir": "Créer automatiquement un dossier pour l'utilisateur", "customStylesheet": "Feuille de style personnalisée", "defaultUserDescription": "Paramètres par défaut pour les nouveaux utilisateurs.", "disableExternalLinks": "Désactiver les liens externes (sauf la documentation)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "documentation", "examples": "Exemples", "executeOnShell": "Exécuter dans le shell", "executeOnShellDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON>er exécute les commandes en appelant directement leurs binaires. Si vous voulez les exécuter sur un shell à la place (comme Bash ou PowerShell), vous pouvez le définir ici avec les arguments et les drapeaux requis. S'il est défini, la commande que vous exécutez sera ajoutée en tant qu'argument. <PERSON><PERSON> s'applique à la fois aux commandes utilisateur et aux crochets d'événements.", "globalRules": "Il s'agit d'un ensemble global de règles d'autorisation et d'interdiction. Elles s'appliquent à tous les utilisateurs. Vous pouvez définir des règles spécifiques sur les paramètres de chaque utilisateur pour remplacer celles-ci.", "globalSettings": "Paramètres généraux", "hideDotfiles": "Cacher les fichiers de configuration utilisateur (dotfiles)", "insertPath": "<PERSON><PERSON><PERSON><PERSON> le chemin", "insertRegex": "Insérez l'expression régulière", "instanceName": "Nom de l'instance", "language": "<PERSON><PERSON>", "lockPassword": "Empêcher l'utilisateur de changer son mot de passe", "newPassword": "Votre nouveau mot de passe", "newPasswordConfirm": "Confirmation du nouveau mot de passe", "newUser": "Nouvel Utilisateur", "password": "Mot de passe", "passwordUpdated": "Mot de passe mis à jour !", "path": "", "perm": {"create": "C<PERSON>er des fichiers et des dossiers", "delete": "Supprimer des fichiers et des dossiers", "download": "Télécharger", "execute": "Exécuter des commandes", "modify": "Modifier des fichiers", "rename": "<PERSON><PERSON> ou déplacer des fichiers ou des dossiers", "share": "Partager des fichiers"}, "permissions": "Permissions", "permissionsHelp": "Vous pouvez définir l'utilisateur comme étant un administrateur ou encore choisir les permissions individuellement. Si vous sélectionnez \"Administrateur\", toutes les autres options seront automatiquement activées. La gestion des utilisateurs est un privilège que seul l'administrateur possède.\n", "profileSettings": "Paramètres du profil", "ruleExample1": "Bloque l'accès à tous les fichiers commençant par un point (comme par exemple .git, .gitignore) dans tous les dossiers", "ruleExample2": "Bloque l'accès au fichier nommé \"Caddyfile\" à la racine du dossier utilisateur", "rules": "<PERSON><PERSON><PERSON>", "rulesHelp": "Vous pouvez définir ici un ensemble de règles pour cet utilisateur. Les fichiers bloqués ne seront pas affichés et ne seront pas accessibles par l'utilisateur. Les expressions régulières sont supportées et les chemins d'accès sont relatifs par rapport au dossier de l'utilisateur.\n", "scope": "Portée du dossier utilisateur", "setDateFormat": "Définir le format de la date", "settingsUpdated": "Les paramètres ont été mis à jour !", "shareDuration": "Du<PERSON>e du partage", "shareManagement": "Gestion des partages", "shareDeleted": "Partage supprimé !", "singleClick": "Utiliser un simple clic pour ouvrir les fichiers et les dossiers", "themes": {"dark": "Sombre", "light": "<PERSON><PERSON><PERSON>", "title": "Thème"}, "user": "Utilisa<PERSON>ur", "userCommands": "Commandes", "userCommandsHelp": "Une liste séparée par des espaces des commandes permises pour l'utilisateur. Exemple :", "userCreated": "Utilisateur créé !", "userDefaults": "User default settings", "userDeleted": "Utilisateur supprimé !", "userManagement": "Gestion des utilisateurs", "userUpdated": "Utilisateur mis à jour !", "username": "Nom d'utilisateur", "users": "Utilisateurs"}, "sidebar": {"help": "Aide", "hugoNew": "Nouveau Hugo", "login": "<PERSON><PERSON>", "logout": "Se déconnecter", "myFiles": "<PERSON><PERSON>", "newFile": "Nouveau fichier", "newFolder": "Nouveau dossier", "preview": "Prévisualiser", "settings": "Paramètres", "signup": "S'inscrire", "siteSettings": "Paramètres du site"}, "success": {"linkCopied": "Lien copié!"}, "time": {"days": "Jours", "hours": "<PERSON><PERSON>", "minutes": "Minutes", "seconds": "Secondes", "unit": "Unité de temps"}}