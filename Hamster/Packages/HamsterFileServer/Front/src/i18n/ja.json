{"buttons": {"cancel": "キャンセル", "close": "閉じる", "copy": "コピー", "copyFile": "ファイルをコピー", "copyToClipboard": "クリップボードにコピー", "create": "作成", "delete": "削除", "download": "ダウンロード", "hideDotfiles": "", "info": "情報", "more": "More", "move": "移動", "moveFile": "ファイルを移動", "new": "新規", "next": "次", "ok": "OK", "permalink": "固定リンク", "previous": "前", "publish": "発表", "rename": "名前を変更", "replace": "置き換える", "reportIssue": "問題を報告", "save": "保存", "schedule": "スケジュール", "search": "検索", "select": "選択", "selectMultiple": "複数選択", "share": "シェア", "shell": "Toggle shell", "switchView": "表示を切り替わる", "toggleSidebar": "サイドバーを表示する", "update": "更新", "upload": "アップロード"}, "download": {"downloadFile": "Download File", "downloadFolder": "Download Folder", "downloadSelected": ""}, "errors": {"forbidden": "You don't have permissions to access this.", "internal": "内部エラーが発生しました。", "notFound": "リソースが見つからなりませんでした。"}, "files": {"body": "本文", "clear": "クリアー", "closePreview": "プレビューを閉じる", "files": "ファイル", "folders": "フォルダ", "home": "ホーム", "lastModified": "最終変更", "loading": "ローディング...", "lonely": "ここには何もない...", "metadata": "メタデータ", "multipleSelectionEnabled": "複数選択有効", "name": "名前", "size": "サイズ", "sortByLastModified": "最終変更日付によるソート", "sortByName": "名前によるソート", "sortBySize": "サイズによるソート"}, "help": {"click": "ファイルやディレクトリを選択", "ctrl": {"click": "複数のファイルやディレクトリを選択", "f": "検索を有効にする", "s": "ファイルを保存またはカレントディレクトリをダウンロード"}, "del": "選択した項目を削除", "doubleClick": "ファイルやディレクトリをオープン", "esc": "選択をクリアーまたはプロンプトを閉じる", "f1": "このヘルプを表示", "f2": "ファイルの名前を変更", "help": "ヘルプ"}, "languages": {"he": "עברית", "hu": "<PERSON><PERSON><PERSON>", "ar": "العربية", "de": "De<PERSON>ch", "en": "English", "es": "Español", "fr": "Français", "is": "", "it": "Italiano", "ja": "日本語", "ko": "한국어", "nlBE": "", "pl": "<PERSON><PERSON>", "pt": "Português", "ptBR": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "ro": "", "ru": "Русский", "sk": "Slovenčina", "svSE": "", "tr": "Türkçe", "ua": "Українська", "zhCN": "中文 (简体)", "zhTW": "中文 (繁體)"}, "login": {"createAnAccount": "Create an account", "loginInstead": "Already have an account", "password": "パスワード", "passwordConfirm": "Password Confirmation", "passwordsDontMatch": "Passwords don't match", "signup": "Signup", "submit": "ログイン", "username": "ユーザ名", "usernameTaken": "Username already taken", "wrongCredentials": "ユーザ名またはパスワードが間違っています。"}, "permanent": "永久", "prompts": {"copy": "コピー", "copyMessage": "コピーの目標ディレクトリを選択してください：", "currentlyNavigating": "現在閲覧しているディレクトリ：", "deleteMessageMultiple": "{count} つのファイルを本当に削除してよろしいですか。", "deleteMessageSingle": "このファイル/フォルダを本当に削除してよろしいですか。", "deleteTitle": "ファイルを削除", "displayName": "名前：", "download": "ファイルをダウンロード", "downloadMessage": "圧縮形式を選択してください。", "error": "あるエラーが発生しました。", "fileInfo": "ファイル情報", "filesSelected": "{count} つのファイルは選択されました。", "lastModified": "最終変更", "move": "移動", "moveMessage": "移動の目標ディレクトリを選択してください：", "newArchetype": "ある元型に基づいて新しいポストを作成します。ファイルは  コンテンツフォルダに作成されます。", "newDir": "新しいディレクトリを作成", "newDirMessage": "新しいディレクトリの名前を入力してください。", "newFile": "新しいファイルを作成", "newFileMessage": "新しいファイルの名前を入力してください。", "numberDirs": "ディレクトリ個数", "numberFiles": "ファイル個数", "rename": "名前を変更", "renameMessage": "名前を変更しようファイルは：", "replace": "置き換える", "replaceMessage": "アップロードするファイルの中でかち合う名前が一つあります。 既存のファイルを置き換えりませんか。\n", "schedule": "スケジュール", "scheduleMessage": "このポストの発表日付をスケジュールしてください。", "show": "表示", "size": "サイズ", "upload": "", "uploadMessage": ""}, "search": {"images": "画像", "music": "音楽", "pdf": "PDF", "pressToSearch": "Press enter to search...", "search": "検索...", "typeToSearch": "Type to search...", "types": "種類", "video": "ビデオ"}, "settings": {"admin": "管理者", "administrator": "管理者", "allowCommands": "コマンドの実行", "allowEdit": "ファイルやディレクトリの編集、名前変更と削除", "allowNew": "ファイルとディレクトリの作成", "allowPublish": "ポストとぺーじの発表", "allowSignup": "Allow users to signup", "avoidChanges": "(変更を避けるために空白にしてください)", "branding": "Branding", "brandingDirectoryPath": "Branding directory path", "brandingHelp": "You can customize how your File Browser instance looks and feels by changing its name, replacing the logo, adding custom styles and even disable external links to GitHub.\nFor more information about custom branding, please check out the {0}.", "changePassword": "パスワードを変更", "commandRunner": "Command runner", "commandRunnerHelp": "Here you can set commands that are executed in the named events. You must write one per line. The environment variables {0} and {1} will be available, being {0} relative to {1}. For more information about this feature and the available environment variables, please read the {2}.", "commandsUpdated": "コマンドは更新されました！", "createUserDir": "Auto create user home dir while adding new user", "customStylesheet": "カスタムスタイルシ ート", "defaultUserDescription": "This are the default settings for new users.", "disableExternalLinks": "Disable external links (except documentation)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "documentation", "examples": "例", "executeOnShell": "Execute on shell", "executeOnShellDescription": "By default, File Browser executes the commands by calling their binaries directly. If you want to run them on a shell instead (such as Bash or PowerShell), you can define it here with the  required arguments and flags. If set, the command you execute will be appended as an argument. This apply to both user commands and event hooks.", "globalRules": "This is a global set of allow and disallow rules. They apply to every user. You can define specific rules on each user's settings to override this ones.", "globalSettings": "グローバル設定", "hideDotfiles": "", "insertPath": "Insert the path", "insertRegex": "Insert regex expression", "instanceName": "Instance name", "language": "言語", "lockPassword": "新しいパスワードを変更に禁止", "newPassword": "新しいパスワード", "newPasswordConfirm": "新しいパスワードを確認します", "newUser": "新しいユーザー", "password": "パスワード", "passwordUpdated": "パスワードは更新されました！", "path": "", "perm": {"create": "Create files and directories", "delete": "Delete files and directories", "download": "Download", "execute": "Execute commands", "modify": "Edit files", "rename": "Rename or move files and directories", "share": "Share files"}, "permissions": "権限", "permissionsHelp": "あなたはユーザーを管理者に設定し、または権限を個々に設定しできます。\"管理者\"を選択する場合、その他のすべての選択肢は自動的に設定されます。ユーザーの管理は管理者の権限として保留されました。", "profileSettings": "プロファイル設定", "ruleExample1": "各フォルダに名前はドットで始まるファイル（例えば、.git、.gitignore）へのアクセスを制限します。", "ruleExample2": "範囲のルートパスに名前は Caddyfile のファイルへのアクセスを制限します。", "rules": "規則", "rulesHelp": "ここに、あなたはこのユーザーの許可または拒否規則を設定できます。ブロックされたファイルはリストに表示されません、それではアクセスも制限されます。正規表現(regex)のサポートと範囲に相対のパスが提供されています。", "scope": "範囲", "settingsUpdated": "設定は更新されました！", "shareDuration": "", "shareManagement": "", "singleClick": "", "themes": {"dark": "", "light": "", "title": ""}, "user": "ユーザー", "userCommands": "ユーザーのコマンド", "userCommandsHelp": "空白区切りの有効のコマンドのリストを指定してください。例：", "userCreated": "ユーザーは作成されました！", "userDefaults": "User default settings", "userDeleted": "ユーザーは削除されました！", "userManagement": "ユーザー管理", "userUpdated": "ユーザーは更新されました！", "username": "ユーザー名", "users": "ユーザー"}, "sidebar": {"help": "ヘルプ", "hugoNew": "<PERSON>", "login": "<PERSON><PERSON>", "logout": "ログアウト", "myFiles": "私のファイル", "newFile": "新しいファイルを作成", "newFolder": "新しいフォルダを作成", "preview": "プレビュー", "settings": "設定", "signup": "Signup", "siteSettings": "サイト設定"}, "success": {"linkCopied": "リンクがコピーされました！"}, "time": {"days": "日", "hours": "時間", "minutes": "分", "seconds": "秒", "unit": "時間単位"}}