.dashboard {
  margin: 1em 0;
}

.dashboard .row {
  display: flex;
  margin: 0 -.5em;
  flex-wrap: wrap;
}

body.rtl .dashboard .row {
  margin-right: 16em;
}

.dashboard .row .column {
  display: flex;
  padding: 0 .5em;
  width: 50%;
}

.dashboard .row .column .card {
  flex-grow: 1;
}

@media(max-width: 1200px) {
  .dashboard .row .column {
    width: 100%;
  }
}

a {
  color: inherit
}

.dashboard p label {
  margin-bottom: .2em;
  display: block;
  font-size: .8em;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.57);
}

li code,
p code {
  background: rgba(0, 0, 0, 0.05);
  padding: .1em;
  border-radius: .2em;
}

.small {
  font-size: .8em;
  line-height: 1.5;
}

.dashboard #nav {
  display: flex;
  padding-bottom: 1em;
  overflow: auto;
}

.dashboard #nav .wrapper {
  display: flex;
  flex-grow: 1;
  border-bottom: 2px solid rgba(0, 0, 0, 0.05);
}

body.rtl #nav .wrapper {
  margin-right: 16em;
}

.dashboard #nav ul {
  list-style: none;
  display: flex;
  color: rgb(84, 110, 122);
  font-weight: 500;
  padding: 0;
  margin: 0 0 -2px 0;
  font-size: .8em;
  text-align: center;
  justify-content: left;
}

.dashboard #nav ul li {
  position: relative;
  padding: 1.5em 2em;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
  transition: .1s ease-in-out all;

}

.dashboard #nav ul li:hover {
  background: var(--moon-grey);
}

.dashboard #nav ul li.active {
  border-color: var(--blue);
  color: var(--blue);
}

.dashboard #nav ul li.active::before {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  background: var(--blue);
  opacity: 0.08;
}

.dashboard #nav i {
  font-size: 1em;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
  width: 100%;
}

table tr {
  border-bottom: 1px solid #ccc;
}

table tr:last-child {
  border: 0;
}

table th {
  font-weight: 500;
  color: #757575;
  text-align: left;
}

table th,
table td {
  padding: .5em 0;
}

table td.small {
  width: 1em;
}

table tr>*:first-child {
  padding-left: 1em;
}

body.rtl table tr>* {
  padding-left: unset;
  padding-right: 1em;
  text-align: right;
  direction: ltr;
}

table tr>*:last-child {
  padding-right: 1em;
}

.card {
  position: relative;
  margin: 0 0 1rem 0;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
  overflow: auto;
}

.card.floating {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
  max-width: 25em;
  width: 90%;
  max-height: 95%;
  animation: .1s show forwards;
}

.card>*>*:first-child {
  margin-top: 0;
}

.card>*>*:last-child {
  margin-bottom: 0;
}

.card .card-title {
  padding: 1.5em 1em 1em;
  display: flex;
}

.card .card-title>*:first-child {
  margin-right: auto;
}

body.rtl .card .card-title>*:first-child {
  margin-right: 0;
  text-align: right;
}

.card>div {
  padding: 1em 1em;
}

.card>div:first-child {
  padding-top: 1.5em;
}

.card>div:last-child {
  padding-bottom: 1.5em;
}

.card .card-title * {
  margin: 0;
}

.card .card-action {
  text-align: right;
}

.card .card-content.full {
  padding-bottom: 0;
  overflow: auto;
}

.card h2 {
  font-weight: 500;
}

.card h3 {
  color: rgba(0, 0, 0, 0.53);
  font-size: 1em;
  font-weight: 500;
  margin: 2em 0 1em;
}

.card-content table {
  margin: 0 -1em;
  width: calc(100% + 2em);
}

.card code {
  word-wrap: break-word;
}

.card#download {
  max-width: 15em;
}

.card#share ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.card#share ul li {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card#share ul li a {
  color: var(--blue);
  cursor: pointer;
  margin-right: auto;
}

.card#share ul li .action i {
  font-size: 1em;
}

.card#share ul li input,
.card#share ul li select {
  padding: .2em;
  margin-right: .5em;
  border: 1px solid #dadada;
}

.card#share .action.copy-clipboard::after {
  content: 'Copied!';
  position: absolute;
  left: -25%;
  width: 150%;
  font-size: .6em;
  text-align: center;
  background: #44a6f5;
  color: #fff;
  padding: .5em .2em;
  border-radius: .4em;
  top: -2em;
  transition: .1s ease opacity;
  opacity: 0;
}

.card#share .action.copy-clipboard.active::after {
  opacity: 1;
}

.card#share .input-group {
  display: flex;
}

.card#share .input-group * {
  border: none;
}

.card#share .input-group input {
  flex: 1;
}

.overlay {
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  animation: .1s show forwards;
}


/* * * * * * * * * * * * * * * *
 *        PROMPT - MOVE        *
 * * * * * * * * * * * * * * * */

.file-list {
  max-height: 50vh;
  overflow: auto;
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
}

.file-list li {
  width: 100%;
  user-select: none;
  border-radius: .2em;
  padding: .3em;
}

.file-list li[aria-selected=true] {
  background: var(--blue) !important;
  color: #fff !important;
  transition: .1s ease all;
}

.file-list li:hover {
  background-color: #e9eaeb;
  cursor: pointer;
}

.file-list li:before {
  content: "folder";
  color: #6f6f6f;
  vertical-align: middle;
  line-height: 1.4;
  font-family: 'Material Icons';
  font-size: 1.75em;
  margin-right: .25em;
}

.file-list li[aria-selected=true]:before {
  color: white;
}

.help {
  max-width: 24em;
}

.help ul {
  padding: 0;
  margin: 1em 0;
  list-style: none;
}

@keyframes show {
  0% {
    visibility: hidden;
    opacity: 0;
  }
  1% {
    visibility: visible;
    opacity: 0;
  }
  100% {
    visibility: visible;
    opacity: 1;
  }
}

.collapsible {
  border-top: 1px solid rgba(0,0,0,0.1);
}

.collapsible:last-of-type {
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.collapsible > input {
  display: none;
}

.collapsible > label {
  padding: 1em 0;
  cursor: pointer;
  border-right: 0;
  border-left: 0;
  display: flex;
  justify-content: space-between;
}

.collapsible > label * {
  margin: 0;
  color: rgba(0,0,0,0.57);
}

.collapsible > label i {
  transition: .2s ease transform;
  user-select: none;
}

.collapsible .collapse {
  max-height: 0;
  overflow: hidden;
  transition: .2s ease all;
}

.collapsible > input:checked ~ .collapse {
  padding-top: 1em;
  padding-bottom: 1em;
  max-height: 20em;
}

.collapsible > input:checked ~ label i {
  transform: rotate(180deg)
}

.card .collapsible {
  width: calc(100% + 2em);
  margin: 0 -1em;
}

.card .collapsible > label {
  padding: 1em;
}

.card .collapsible .collapse {
  padding: 0 1em;
}

.card .card-action.full {
  padding-top: 0;
  display: flex;
  flex-wrap: wrap;
}

.card .card-action.full .action {
  flex: 1;
  padding: 2em;
  border-radius: 0.2em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
}

.card .card-action.full .action {
  margin: 0 0.25em 0.50em;
}

.card .card-action.full .action i {
  display: block;
  padding: 0;
  margin-bottom: 0.25em;
  font-size: 4em;
}

.card .card-action.full .action .title {
  font-size: 1.5em;
  font-weight: 500;
}

/*** RTL - Fix disk usage information (in english) ***/
body.rtl .credits {
  text-align: right;
  direction: ltr;
}