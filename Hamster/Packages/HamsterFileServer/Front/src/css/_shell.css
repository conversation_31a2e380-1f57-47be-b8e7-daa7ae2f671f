.shell {
  position: fixed;
  bottom: 0;
  left: 0;
  max-height: calc(100% - 4em);
  background: white;
  color: #212121;
  z-index: 9997;
  width: 100%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  transition: .2s ease transform;
}

.shell__divider {
  position: relative;
  height: 8px;
  z-index: 9999;
  background: rgba(127, 127, 127, 0.1);
  transition: 0.2s ease background;
  cursor: ns-resize;
  touch-action: none;
  user-select: none;
}

.shell__divider:hover {
  background: rgba(127, 127, 127, 0.4);
}

.shell__content {
  height: 100%;
  font-family: monospace;
  overflow: auto;
  font-size: 1rem;
  cursor: text;
}

.shell__overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  z-index: 9998;
  background-color: rgba(0, 0, 0, 0.05);
}

body.rtl .shell-content {
  direction: ltr;
}

.shell__result {
  display: flex;
  padding: 0.5em;
  align-items: flex-start;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.shell--hidden {
  transform: translateY(105%);
}

.shell__result--hidden {
  opacity: 0;
}

.shell__text,
.shell__prompt,
.shell__prompt i {
  font-size: inherit;
}

.shell__prompt {
  width: 1.2rem;
}

.shell__prompt i {
  color: var(--blue);
}

.shell__text {
  margin: 0;
  font-family: inherit;
  white-space: pre-wrap;
  width: 100%;
}
