// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		C00C26B72B0698E300E92F89 /* HamsterKeyboardKit in Frameworks */ = {isa = PBXBuildFile; productRef = C00C26B62B0698E300E92F89 /* HamsterKeyboardKit */; };
		C00C26C72B069D6A00E92F89 /* boost_filesystem.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BB2B069D6900E92F89 /* boost_filesystem.xcframework */; };
		C00C26C82B069D6A00E92F89 /* boost_filesystem.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BB2B069D6900E92F89 /* boost_filesystem.xcframework */; };
		C00C26C92B069D6A00E92F89 /* boost_filesystem.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BB2B069D6900E92F89 /* boost_filesystem.xcframework */; };
		C00C26CA2B069D6A00E92F89 /* boost_regex.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BC2B069D6900E92F89 /* boost_regex.xcframework */; };
		C00C26CB2B069D6A00E92F89 /* boost_regex.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BC2B069D6900E92F89 /* boost_regex.xcframework */; };
		C00C26CC2B069D6A00E92F89 /* boost_regex.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BC2B069D6900E92F89 /* boost_regex.xcframework */; };
		C00C26CD2B069D6A00E92F89 /* boost_system.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BD2B069D6900E92F89 /* boost_system.xcframework */; };
		C00C26CE2B069D6A00E92F89 /* boost_system.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BD2B069D6900E92F89 /* boost_system.xcframework */; };
		C00C26CF2B069D6A00E92F89 /* boost_system.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BD2B069D6900E92F89 /* boost_system.xcframework */; };
		C00C26D02B069D6A00E92F89 /* libglog.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BE2B069D6900E92F89 /* libglog.xcframework */; };
		C00C26D12B069D6A00E92F89 /* libglog.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BE2B069D6900E92F89 /* libglog.xcframework */; };
		C00C26D22B069D6A00E92F89 /* libglog.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BE2B069D6900E92F89 /* libglog.xcframework */; };
		C00C26D32B069D6A00E92F89 /* libyaml-cpp.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BF2B069D6900E92F89 /* libyaml-cpp.xcframework */; };
		C00C26D42B069D6A00E92F89 /* libyaml-cpp.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BF2B069D6900E92F89 /* libyaml-cpp.xcframework */; };
		C00C26D52B069D6A00E92F89 /* libyaml-cpp.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26BF2B069D6900E92F89 /* libyaml-cpp.xcframework */; };
		C00C26D62B069D6A00E92F89 /* libleveldb.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C02B069D6A00E92F89 /* libleveldb.xcframework */; };
		C00C26D72B069D6A00E92F89 /* libleveldb.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C02B069D6A00E92F89 /* libleveldb.xcframework */; };
		C00C26D82B069D6A00E92F89 /* libleveldb.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C02B069D6A00E92F89 /* libleveldb.xcframework */; };
		C00C26DB2B069D6A00E92F89 /* librime-sbxlm.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C12B069D6A00E92F89 /* librime-sbxlm.xcframework */; };
		C00C26DC2B069D6A00E92F89 /* boost_atomic.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C22B069D6A00E92F89 /* boost_atomic.xcframework */; };
		C00C26DD2B069D6A00E92F89 /* boost_atomic.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C22B069D6A00E92F89 /* boost_atomic.xcframework */; };
		C00C26DE2B069D6A00E92F89 /* boost_atomic.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C22B069D6A00E92F89 /* boost_atomic.xcframework */; };
		C00C26DF2B069D6A00E92F89 /* libopencc.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C32B069D6A00E92F89 /* libopencc.xcframework */; };
		C00C26E02B069D6A00E92F89 /* libopencc.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C32B069D6A00E92F89 /* libopencc.xcframework */; };
		C00C26E12B069D6A00E92F89 /* libopencc.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C32B069D6A00E92F89 /* libopencc.xcframework */; };
		C00C26E22B069D6A00E92F89 /* boost_locale.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C42B069D6A00E92F89 /* boost_locale.xcframework */; };
		C00C26E32B069D6A00E92F89 /* boost_locale.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C42B069D6A00E92F89 /* boost_locale.xcframework */; };
		C00C26E42B069D6A00E92F89 /* boost_locale.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C42B069D6A00E92F89 /* boost_locale.xcframework */; };
		C00C26E52B069D6A00E92F89 /* libmarisa.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C52B069D6A00E92F89 /* libmarisa.xcframework */; };
		C00C26E62B069D6B00E92F89 /* libmarisa.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C52B069D6A00E92F89 /* libmarisa.xcframework */; };
		C00C26E72B069D6B00E92F89 /* libmarisa.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C52B069D6A00E92F89 /* libmarisa.xcframework */; };
		C00C26E82B069D6B00E92F89 /* librime.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C62B069D6A00E92F89 /* librime.xcframework */; };
		C00C26E92B069D6B00E92F89 /* librime.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = C00C26C62B069D6A00E92F89 /* librime.xcframework */; };
		C00C26EC2B069E0400E92F89 /* HamsterKeyboardKit in Frameworks */ = {isa = PBXBuildFile; productRef = C00C26EB2B069E0400E92F89 /* HamsterKeyboardKit */; };
		C01396FC2AB499D80043674A /* RimeSyncIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = C01396F92AB499CA0043674A /* RimeSyncIntent.swift */; };
		C01396FD2AB499D80043674A /* RimeDeployIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = C01396FA2AB499CA0043674A /* RimeDeployIntent.swift */; };
		C02435B42A0490230080CFB4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = C02435932A0490220080CFB4 /* Assets.xcassets */; };
		C02435B52A0490230080CFB4 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = C02435952A0490220080CFB4 /* Preview Assets.xcassets */; };
		C02C4D982AC105CA00FF91BA /* IntentProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = C02C4D972AC105CA00FF91BA /* IntentProvider.swift */; };
		C03319DA2A2DFEBB00CE4950 /* AppDelegete.swift in Sources */ = {isa = PBXBuildFile; fileRef = C03319D92A2DFEBB00CE4950 /* AppDelegete.swift */; };
		C03319DC2A2DFEF700CE4950 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = C03319DB2A2DFEF700CE4950 /* SceneDelegate.swift */; };
		C03319E22A2E1BEA00CE4950 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = C03319E12A2E1BEA00CE4950 /* LaunchScreen.storyboard */; };
		C03EC39A2B06491E00DB741E /* HamsterKeyboard.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = C03E803C296D53650083E53A /* HamsterKeyboard.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		C03EC3A62B06670F00DB741E /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = C03EC3A82B06670F00DB741E /* InfoPlist.strings */; };
		C03EC3BD2B066C1100DB741E /* KeyboardViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = C03EC3BC2B066C1100DB741E /* KeyboardViewController.swift */; };
		C03EC3C12B066C1100DB741E /* SbxlmKeyboard.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = C03EC3BA2B066C1100DB741E /* SbxlmKeyboard.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		C03EC3CC2B066D4100DB741E /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = C03EC3CE2B066D4100DB741E /* InfoPlist.strings */; };
		C03EC3D32B066DDC00DB741E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = C02435932A0490220080CFB4 /* Assets.xcassets */; };
		C053ADC32A5D503200EA7EEE /* HamsteriOS in Frameworks */ = {isa = PBXBuildFile; productRef = C053ADC22A5D503200EA7EEE /* HamsteriOS */; };
		C09F6AF72A07FB3900F2EF18 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = C02435932A0490220080CFB4 /* Assets.xcassets */; };
		C0A602B32A185D88000DC924 /* HamsterKeyboardInputViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = C000CA9D296EA72600EC5323 /* HamsterKeyboardInputViewController.swift */; };
		C0C7EAEC29E5A236008D4EE1 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = C0C7EAEF29E5A236008D4EE1 /* InfoPlist.strings */; };
		C0D672B429F1E0E7002D0439 /* SharedSupport.zip in Copy Rime SharedSupport */ = {isa = PBXBuildFile; fileRef = C0D672B329F1E066002D0439 /* SharedSupport.zip */; };
		C0D856CC2B026FF6003DB275 /* rime-ice.zip in Copy Rime SharedSupport */ = {isa = PBXBuildFile; fileRef = C0D856CA2B026FDA003DB275 /* rime-ice.zip */; };
		C0DA3F422AFB5AE9003E74DA /* RimeDeployBackgroundIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0DA3F412AFB5AE9003E74DA /* RimeDeployBackgroundIntent.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		C03EC39B2B06491E00DB741E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C03E801D296D4F4C0083E53A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C03E803B296D53650083E53A;
			remoteInfo = HamsterKeyboard;
		};
		C03EC3BF2B066C1100DB741E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C03E801D296D4F4C0083E53A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C03EC3B92B066C1100DB741E;
			remoteInfo = SbxlmKeyboard;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		C03EC39D2B06491E00DB741E /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				C03EC3C12B066C1100DB741E /* SbxlmKeyboard.appex in Embed Foundation Extensions */,
				C03EC39A2B06491E00DB741E /* HamsterKeyboard.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		C071BF712970126C00E21FBC /* Copy Rime SharedSupport */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 12;
			files = (
				C0D856CC2B026FF6003DB275 /* rime-ice.zip in Copy Rime SharedSupport */,
				C0D672B429F1E0E7002D0439 /* SharedSupport.zip in Copy Rime SharedSupport */,
			);
			name = "Copy Rime SharedSupport";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		C000CA9D296EA72600EC5323 /* HamsterKeyboardInputViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HamsterKeyboardInputViewController.swift; sourceTree = "<group>"; };
		C00749622ABAD48C00C5CD0A /* HamsterFileServer */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = HamsterFileServer; path = Packages/HamsterFileServer; sourceTree = "<group>"; };
		C00C26BB2B069D6900E92F89 /* boost_filesystem.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_filesystem.xcframework; sourceTree = "<group>"; };
		C00C26BC2B069D6900E92F89 /* boost_regex.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_regex.xcframework; sourceTree = "<group>"; };
		C00C26BD2B069D6900E92F89 /* boost_system.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_system.xcframework; sourceTree = "<group>"; };
		C00C26BE2B069D6900E92F89 /* libglog.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = libglog.xcframework; sourceTree = "<group>"; };
		C00C26BF2B069D6900E92F89 /* libyaml-cpp.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = "libyaml-cpp.xcframework"; sourceTree = "<group>"; };
		C00C26C02B069D6A00E92F89 /* libleveldb.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = libleveldb.xcframework; sourceTree = "<group>"; };
		C00C26C12B069D6A00E92F89 /* librime-sbxlm.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = "librime-sbxlm.xcframework"; sourceTree = "<group>"; };
		C00C26C22B069D6A00E92F89 /* boost_atomic.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_atomic.xcframework; sourceTree = "<group>"; };
		C00C26C32B069D6A00E92F89 /* libopencc.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = libopencc.xcframework; sourceTree = "<group>"; };
		C00C26C42B069D6A00E92F89 /* boost_locale.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_locale.xcframework; sourceTree = "<group>"; };
		C00C26C52B069D6A00E92F89 /* libmarisa.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = libmarisa.xcframework; sourceTree = "<group>"; };
		C00C26C62B069D6A00E92F89 /* librime.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = librime.xcframework; sourceTree = "<group>"; };
		C00E006C2A1B6AFD00180F81 /* zh */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = zh; path = zh.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		C01396F92AB499CA0043674A /* RimeSyncIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RimeSyncIntent.swift; sourceTree = "<group>"; };
		C01396FA2AB499CA0043674A /* RimeDeployIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RimeDeployIntent.swift; sourceTree = "<group>"; };
		C02435932A0490220080CFB4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		C02435952A0490220080CFB4 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		C02435962A0490220080CFB4 /* Hamster.entitlements */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.entitlements; path = Hamster.entitlements; sourceTree = "<group>"; };
		C02435972A0490220080CFB4 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		C02C4D972AC105CA00FF91BA /* IntentProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntentProvider.swift; sourceTree = "<group>"; };
		C03319D92A2DFEBB00CE4950 /* AppDelegete.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegete.swift; sourceTree = "<group>"; };
		C03319DB2A2DFEF700CE4950 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		C03319E12A2E1BEA00CE4950 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		C03E8025296D4F4C0083E53A /* Hamster.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Hamster.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C03E803C296D53650083E53A /* HamsterKeyboard.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = HamsterKeyboard.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		C03E8040296D53650083E53A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		C03EC3A72B06670F00DB741E /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C03EC3A92B06671600DB741E /* zh */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = zh; path = zh.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		C03EC3AA2B06671800DB741E /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C03EC3AB2B06671900DB741E /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C03EC3AC2B06671A00DB741E /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		C03EC3BA2B066C1100DB741E /* SbxlmKeyboard.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = SbxlmKeyboard.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		C03EC3BC2B066C1100DB741E /* KeyboardViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeyboardViewController.swift; sourceTree = "<group>"; };
		C03EC3BE2B066C1100DB741E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		C03EC3C52B066C4A00DB741E /* SbxlmKeyboard.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SbxlmKeyboard.entitlements; sourceTree = "<group>"; };
		C03EC3CD2B066D4100DB741E /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C03EC3CF2B066D4300DB741E /* zh */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = zh; path = zh.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		C03EC3D02B066D4400DB741E /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C03EC3D12B066D4500DB741E /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C03EC3D22B066D4600DB741E /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		C071BECB2970072100E21FBC /* HamsterKeyboard.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = HamsterKeyboard.entitlements; sourceTree = "<group>"; };
		C08387F12A55897700EA8082 /* RimeKit */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = RimeKit; path = Packages/RimeKit; sourceTree = "<group>"; };
		C08387F32A5589A400EA8082 /* HamsterUIKit */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = HamsterUIKit; path = Packages/HamsterUIKit; sourceTree = "<group>"; };
		C08387F42A5589AF00EA8082 /* HamsterKit */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = HamsterKit; path = Packages/HamsterKit; sourceTree = "<group>"; };
		C08387F52A5589DF00EA8082 /* HamsteriOS */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = HamsteriOS; path = Packages/HamsteriOS; sourceTree = "<group>"; };
		C0C642842AC561180084147F /* ci_post_clone.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = ci_post_clone.sh; sourceTree = "<group>"; };
		C0C7EAEE29E5A236008D4EE1 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C0C7EAF029E5A23A008D4EE1 /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C0C7EAF129E5A23B008D4EE1 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		C0C7EAF229E5A23C008D4EE1 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		C0C8B57E2A823BBF009972C1 /* HamsterKeyboardKit */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = HamsterKeyboardKit; path = Packages/HamsterKeyboardKit; sourceTree = "<group>"; };
		C0C9041C2A56412200AF3F15 /* hamster.yaml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = hamster.yaml; sourceTree = "<group>"; };
		C0D672B329F1E066002D0439 /* SharedSupport.zip */ = {isa = PBXFileReference; lastKnownFileType = archive.zip; path = SharedSupport.zip; sourceTree = "<group>"; };
		C0D856C92B0213E5003DB275 /* HamsterDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = HamsterDebug.entitlements; sourceTree = "<group>"; };
		C0D856CA2B026FDA003DB275 /* rime-ice.zip */ = {isa = PBXFileReference; lastKnownFileType = archive.zip; path = "rime-ice.zip"; sourceTree = "<group>"; };
		C0DA3F412AFB5AE9003E74DA /* RimeDeployBackgroundIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RimeDeployBackgroundIntent.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		C03E8022296D4F4C0083E53A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C00C26C72B069D6A00E92F89 /* boost_filesystem.xcframework in Frameworks */,
				C00C26D62B069D6A00E92F89 /* libleveldb.xcframework in Frameworks */,
				C00C26D02B069D6A00E92F89 /* libglog.xcframework in Frameworks */,
				C00C26D32B069D6A00E92F89 /* libyaml-cpp.xcframework in Frameworks */,
				C00C26E52B069D6A00E92F89 /* libmarisa.xcframework in Frameworks */,
				C00C26E22B069D6A00E92F89 /* boost_locale.xcframework in Frameworks */,
				C053ADC32A5D503200EA7EEE /* HamsteriOS in Frameworks */,
				C00C26CD2B069D6A00E92F89 /* boost_system.xcframework in Frameworks */,
				C00C26DC2B069D6A00E92F89 /* boost_atomic.xcframework in Frameworks */,
				C00C26E82B069D6B00E92F89 /* librime.xcframework in Frameworks */,
				C00C26CA2B069D6A00E92F89 /* boost_regex.xcframework in Frameworks */,
				C00C26DF2B069D6A00E92F89 /* libopencc.xcframework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C03E8039296D53650083E53A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C00C26C82B069D6A00E92F89 /* boost_filesystem.xcframework in Frameworks */,
				C00C26D72B069D6A00E92F89 /* libleveldb.xcframework in Frameworks */,
				C00C26D12B069D6A00E92F89 /* libglog.xcframework in Frameworks */,
				C00C26D42B069D6A00E92F89 /* libyaml-cpp.xcframework in Frameworks */,
				C00C26E62B069D6B00E92F89 /* libmarisa.xcframework in Frameworks */,
				C00C26E32B069D6A00E92F89 /* boost_locale.xcframework in Frameworks */,
				C00C26B72B0698E300E92F89 /* HamsterKeyboardKit in Frameworks */,
				C00C26CE2B069D6A00E92F89 /* boost_system.xcframework in Frameworks */,
				C00C26DD2B069D6A00E92F89 /* boost_atomic.xcframework in Frameworks */,
				C00C26E92B069D6B00E92F89 /* librime.xcframework in Frameworks */,
				C00C26CB2B069D6A00E92F89 /* boost_regex.xcframework in Frameworks */,
				C00C26E02B069D6A00E92F89 /* libopencc.xcframework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C03EC3B72B066C1100DB741E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C00C26C92B069D6A00E92F89 /* boost_filesystem.xcframework in Frameworks */,
				C00C26D82B069D6A00E92F89 /* libleveldb.xcframework in Frameworks */,
				C00C26D22B069D6A00E92F89 /* libglog.xcframework in Frameworks */,
				C00C26EC2B069E0400E92F89 /* HamsterKeyboardKit in Frameworks */,
				C00C26D52B069D6A00E92F89 /* libyaml-cpp.xcframework in Frameworks */,
				C00C26E72B069D6B00E92F89 /* libmarisa.xcframework in Frameworks */,
				C00C26E42B069D6A00E92F89 /* boost_locale.xcframework in Frameworks */,
				C00C26CF2B069D6A00E92F89 /* boost_system.xcframework in Frameworks */,
				C00C26DE2B069D6A00E92F89 /* boost_atomic.xcframework in Frameworks */,
				C00C26CC2B069D6A00E92F89 /* boost_regex.xcframework in Frameworks */,
				C00C26DB2B069D6A00E92F89 /* librime-sbxlm.xcframework in Frameworks */,
				C00C26E12B069D6A00E92F89 /* libopencc.xcframework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C00C26BA2B069D3500E92F89 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C00C26C22B069D6A00E92F89 /* boost_atomic.xcframework */,
				C00C26BB2B069D6900E92F89 /* boost_filesystem.xcframework */,
				C00C26C42B069D6A00E92F89 /* boost_locale.xcframework */,
				C00C26BC2B069D6900E92F89 /* boost_regex.xcframework */,
				C00C26BD2B069D6900E92F89 /* boost_system.xcframework */,
				C00C26BE2B069D6900E92F89 /* libglog.xcframework */,
				C00C26C02B069D6A00E92F89 /* libleveldb.xcframework */,
				C00C26C52B069D6A00E92F89 /* libmarisa.xcframework */,
				C00C26C32B069D6A00E92F89 /* libopencc.xcframework */,
				C00C26C12B069D6A00E92F89 /* librime-sbxlm.xcframework */,
				C00C26C62B069D6A00E92F89 /* librime.xcframework */,
				C00C26BF2B069D6900E92F89 /* libyaml-cpp.xcframework */,
			);
			path = Frameworks;
			sourceTree = "<group>";
		};
		C01396F62AB499CA0043674A /* Shortcuts */ = {
			isa = PBXGroup;
			children = (
				C01396F82AB499CA0043674A /* Actions */,
				C02C4D972AC105CA00FF91BA /* IntentProvider.swift */,
			);
			path = Shortcuts;
			sourceTree = "<group>";
		};
		C01396F82AB499CA0043674A /* Actions */ = {
			isa = PBXGroup;
			children = (
				C01396F92AB499CA0043674A /* RimeSyncIntent.swift */,
				C01396FA2AB499CA0043674A /* RimeDeployIntent.swift */,
				C0DA3F412AFB5AE9003E74DA /* RimeDeployBackgroundIntent.swift */,
			);
			path = Actions;
			sourceTree = "<group>";
		};
		C02435722A0490220080CFB4 /* Hamster */ = {
			isa = PBXGroup;
			children = (
				C02435972A0490220080CFB4 /* Info.plist */,
				C02435962A0490220080CFB4 /* Hamster.entitlements */,
				C0D856C92B0213E5003DB275 /* HamsterDebug.entitlements */,
				C01396F62AB499CA0043674A /* Shortcuts */,
				C08387EE2A55467900EA8082 /* Assets */,
				C03319D92A2DFEBB00CE4950 /* AppDelegete.swift */,
				C03319DB2A2DFEF700CE4950 /* SceneDelegate.swift */,
			);
			path = Hamster;
			sourceTree = "<group>";
		};
		C02435942A0490220080CFB4 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				C02435952A0490220080CFB4 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		C03E801C296D4F4C0083E53A = {
			isa = PBXGroup;
			children = (
				C00C26BA2B069D3500E92F89 /* Frameworks */,
				C0C642812AC55E2E0084147F /* ci_scripts */,
				C02435722A0490220080CFB4 /* Hamster */,
				C03E803D296D53650083E53A /* HamsterKeyboard */,
				C03EC3BB2B066C1100DB741E /* SbxlmKeyboard */,
				C08387E82A55101A00EA8082 /* Packages */,
				C071BECC2970108000E21FBC /* Resources */,
				C03E8026296D4F4C0083E53A /* Products */,
			);
			sourceTree = "<group>";
		};
		C03E8026296D4F4C0083E53A /* Products */ = {
			isa = PBXGroup;
			children = (
				C03E8025296D4F4C0083E53A /* Hamster.app */,
				C03E803C296D53650083E53A /* HamsterKeyboard.appex */,
				C03EC3BA2B066C1100DB741E /* SbxlmKeyboard.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C03E803D296D53650083E53A /* HamsterKeyboard */ = {
			isa = PBXGroup;
			children = (
				C071BECB2970072100E21FBC /* HamsterKeyboard.entitlements */,
				C03E8040296D53650083E53A /* Info.plist */,
				C000CA9D296EA72600EC5323 /* HamsterKeyboardInputViewController.swift */,
				C03EC3A82B06670F00DB741E /* InfoPlist.strings */,
			);
			path = HamsterKeyboard;
			sourceTree = "<group>";
		};
		C03EC3BB2B066C1100DB741E /* SbxlmKeyboard */ = {
			isa = PBXGroup;
			children = (
				C03EC3C52B066C4A00DB741E /* SbxlmKeyboard.entitlements */,
				C03EC3BC2B066C1100DB741E /* KeyboardViewController.swift */,
				C03EC3BE2B066C1100DB741E /* Info.plist */,
				C03EC3CE2B066D4100DB741E /* InfoPlist.strings */,
			);
			path = SbxlmKeyboard;
			sourceTree = "<group>";
		};
		C071BECC2970108000E21FBC /* Resources */ = {
			isa = PBXGroup;
			children = (
				C0D671B029F16D76002D0439 /* SharedSupport */,
				C0C7EAEF29E5A236008D4EE1 /* InfoPlist.strings */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		C08387E82A55101A00EA8082 /* Packages */ = {
			isa = PBXGroup;
			children = (
				C08387F52A5589DF00EA8082 /* HamsteriOS */,
				C08387F32A5589A400EA8082 /* HamsterUIKit */,
				C08387F42A5589AF00EA8082 /* HamsterKit */,
				C08387F12A55897700EA8082 /* RimeKit */,
				C0C8B57E2A823BBF009972C1 /* HamsterKeyboardKit */,
				C00749622ABAD48C00C5CD0A /* HamsterFileServer */,
			);
			name = Packages;
			sourceTree = "<group>";
		};
		C08387EE2A55467900EA8082 /* Assets */ = {
			isa = PBXGroup;
			children = (
				C03319E12A2E1BEA00CE4950 /* LaunchScreen.storyboard */,
				C02435932A0490220080CFB4 /* Assets.xcassets */,
				C02435942A0490220080CFB4 /* Preview Content */,
			);
			path = Assets;
			sourceTree = "<group>";
		};
		C0C642812AC55E2E0084147F /* ci_scripts */ = {
			isa = PBXGroup;
			children = (
				C0C642842AC561180084147F /* ci_post_clone.sh */,
			);
			path = ci_scripts;
			sourceTree = "<group>";
		};
		C0D671B029F16D76002D0439 /* SharedSupport */ = {
			isa = PBXGroup;
			children = (
				C0D856CA2B026FDA003DB275 /* rime-ice.zip */,
				C0C9041C2A56412200AF3F15 /* hamster.yaml */,
				C0D672B329F1E066002D0439 /* SharedSupport.zip */,
			);
			path = SharedSupport;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C03E8024296D4F4C0083E53A /* Hamster */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C03E8033296D4F4D0083E53A /* Build configuration list for PBXNativeTarget "Hamster" */;
			buildPhases = (
				C03E8021296D4F4C0083E53A /* Sources */,
				C03E8022296D4F4C0083E53A /* Frameworks */,
				C0AC2E61299F743E00BC494C /* Resources */,
				C071BF712970126C00E21FBC /* Copy Rime SharedSupport */,
				C03EC39D2B06491E00DB741E /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				C03EC39C2B06491E00DB741E /* PBXTargetDependency */,
				C03EC3C02B066C1100DB741E /* PBXTargetDependency */,
			);
			name = Hamster;
			packageProductDependencies = (
				C053ADC22A5D503200EA7EEE /* HamsteriOS */,
			);
			productName = Hamster;
			productReference = C03E8025296D4F4C0083E53A /* Hamster.app */;
			productType = "com.apple.product-type.application";
		};
		C03E803B296D53650083E53A /* HamsterKeyboard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C03E8044296D53650083E53A /* Build configuration list for PBXNativeTarget "HamsterKeyboard" */;
			buildPhases = (
				C03E8038296D53650083E53A /* Sources */,
				C03E8039296D53650083E53A /* Frameworks */,
				C03E803A296D53650083E53A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = HamsterKeyboard;
			packageProductDependencies = (
				C00C26B62B0698E300E92F89 /* HamsterKeyboardKit */,
			);
			productName = HamsterKeyboard;
			productReference = C03E803C296D53650083E53A /* HamsterKeyboard.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		C03EC3B92B066C1100DB741E /* SbxlmKeyboard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C03EC3C22B066C1100DB741E /* Build configuration list for PBXNativeTarget "SbxlmKeyboard" */;
			buildPhases = (
				C03EC3B62B066C1100DB741E /* Sources */,
				C03EC3B72B066C1100DB741E /* Frameworks */,
				C03EC3B82B066C1100DB741E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SbxlmKeyboard;
			packageProductDependencies = (
				C00C26EB2B069E0400E92F89 /* HamsterKeyboardKit */,
			);
			productName = SbxlmKeyboard;
			productReference = C03EC3BA2B066C1100DB741E /* SbxlmKeyboard.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C03E801D296D4F4C0083E53A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					C03E8024296D4F4C0083E53A = {
						CreatedOnToolsVersion = 14.2;
					};
					C03E803B296D53650083E53A = {
						CreatedOnToolsVersion = 14.2;
						LastSwiftMigration = 1420;
					};
					C03EC3B92B066C1100DB741E = {
						CreatedOnToolsVersion = 15.0.1;
					};
				};
			};
			buildConfigurationList = C03E8020296D4F4C0083E53A /* Build configuration list for PBXProject "Hamster" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				"zh-Hans",
				"zh-HK",
				"zh-Hant",
				Base,
				zh,
			);
			mainGroup = C03E801C296D4F4C0083E53A;
			packageReferences = (
			);
			productRefGroup = C03E8026296D4F4C0083E53A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C03E8024296D4F4C0083E53A /* Hamster */,
				C03E803B296D53650083E53A /* HamsterKeyboard */,
				C03EC3B92B066C1100DB741E /* SbxlmKeyboard */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C03E803A296D53650083E53A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C09F6AF72A07FB3900F2EF18 /* Assets.xcassets in Resources */,
				C03EC3A62B06670F00DB741E /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C03EC3B82B066C1100DB741E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C03EC3D32B066DDC00DB741E /* Assets.xcassets in Resources */,
				C03EC3CC2B066D4100DB741E /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C0AC2E61299F743E00BC494C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C02435B52A0490230080CFB4 /* Preview Assets.xcassets in Resources */,
				C03319E22A2E1BEA00CE4950 /* LaunchScreen.storyboard in Resources */,
				C02435B42A0490230080CFB4 /* Assets.xcassets in Resources */,
				C0C7EAEC29E5A236008D4EE1 /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C03E8021296D4F4C0083E53A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C01396FC2AB499D80043674A /* RimeSyncIntent.swift in Sources */,
				C01396FD2AB499D80043674A /* RimeDeployIntent.swift in Sources */,
				C0DA3F422AFB5AE9003E74DA /* RimeDeployBackgroundIntent.swift in Sources */,
				C03319DC2A2DFEF700CE4950 /* SceneDelegate.swift in Sources */,
				C03319DA2A2DFEBB00CE4950 /* AppDelegete.swift in Sources */,
				C02C4D982AC105CA00FF91BA /* IntentProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C03E8038296D53650083E53A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C0A602B32A185D88000DC924 /* HamsterKeyboardInputViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C03EC3B62B066C1100DB741E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C03EC3BD2B066C1100DB741E /* KeyboardViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C03EC39C2B06491E00DB741E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C03E803B296D53650083E53A /* HamsterKeyboard */;
			targetProxy = C03EC39B2B06491E00DB741E /* PBXContainerItemProxy */;
		};
		C03EC3C02B066C1100DB741E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C03EC3B92B066C1100DB741E /* SbxlmKeyboard */;
			targetProxy = C03EC3BF2B066C1100DB741E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		C03EC3A82B06670F00DB741E /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				C03EC3A72B06670F00DB741E /* zh-Hans */,
				C03EC3A92B06671600DB741E /* zh */,
				C03EC3AA2B06671800DB741E /* zh-HK */,
				C03EC3AB2B06671900DB741E /* zh-Hant */,
				C03EC3AC2B06671A00DB741E /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		C03EC3CE2B066D4100DB741E /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				C03EC3CD2B066D4100DB741E /* zh-Hans */,
				C03EC3CF2B066D4300DB741E /* zh */,
				C03EC3D02B066D4400DB741E /* zh-HK */,
				C03EC3D12B066D4500DB741E /* zh-Hant */,
				C03EC3D22B066D4600DB741E /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		C0C7EAEF29E5A236008D4EE1 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				C0C7EAEE29E5A236008D4EE1 /* zh-Hans */,
				C0C7EAF029E5A23A008D4EE1 /* zh-HK */,
				C0C7EAF129E5A23B008D4EE1 /* zh-Hant */,
				C0C7EAF229E5A23C008D4EE1 /* en */,
				C00E006C2A1B6AFD00180F81 /* zh */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		C03E8031296D4F4D0083E53A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"-Xlinker",
					"-interposable",
				);
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C03E8032296D4F4D0083E53A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"-Xlinker",
					"-interposable",
				);
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C03E8034296D4F4D0083E53A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Hamster/HamsterDebug.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Hamster/Assets/Preview Content\"";
				DEVELOPMENT_TEAM = M4N6995A28;
				ENABLE_BITCODE = NO;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Hamster/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Hamster;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIRequiresFullScreen = NO;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportsDocumentBrowser = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = dev.fuxiao.app.Hamster;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C03E8035296D4F4D0083E53A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Hamster/Hamster.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Hamster/Assets/Preview Content\"";
				DEVELOPMENT_TEAM = M4N6995A28;
				ENABLE_BITCODE = NO;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Hamster/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Hamster;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIRequiresFullScreen = NO;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportsDocumentBrowser = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = dev.fuxiao.app.Hamster;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		C03E8045296D53650083E53A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = HamsterKeyboard/HamsterKeyboard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = M4N6995A28;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = HamsterKeyboard/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = HamsterKeyboard;
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = dev.fuxiao.app.Hamster.HamsterKeyboard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C03E8046296D53650083E53A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = HamsterKeyboard/HamsterKeyboard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = M4N6995A28;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = HamsterKeyboard/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = HamsterKeyboard;
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = dev.fuxiao.app.Hamster.HamsterKeyboard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		C03EC3C32B066C1100DB741E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = SbxlmKeyboard/SbxlmKeyboard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = M4N6995A28;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SbxlmKeyboard/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SbxlmKeyboard;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = dev.fuxiao.app.Hamster.SbxlmKeyboard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C03EC3C42B066C1100DB741E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = SbxlmKeyboard/SbxlmKeyboard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = M4N6995A28;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SbxlmKeyboard/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SbxlmKeyboard;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = dev.fuxiao.app.Hamster.SbxlmKeyboard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C03E8020296D4F4C0083E53A /* Build configuration list for PBXProject "Hamster" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C03E8031296D4F4D0083E53A /* Debug */,
				C03E8032296D4F4D0083E53A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C03E8033296D4F4D0083E53A /* Build configuration list for PBXNativeTarget "Hamster" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C03E8034296D4F4D0083E53A /* Debug */,
				C03E8035296D4F4D0083E53A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C03E8044296D53650083E53A /* Build configuration list for PBXNativeTarget "HamsterKeyboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C03E8045296D53650083E53A /* Debug */,
				C03E8046296D53650083E53A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C03EC3C22B066C1100DB741E /* Build configuration list for PBXNativeTarget "SbxlmKeyboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C03EC3C32B066C1100DB741E /* Debug */,
				C03EC3C42B066C1100DB741E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCSwiftPackageProductDependency section */
		C00C26B62B0698E300E92F89 /* HamsterKeyboardKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = HamsterKeyboardKit;
		};
		C00C26EB2B069E0400E92F89 /* HamsterKeyboardKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = HamsterKeyboardKit;
		};
		C053ADC22A5D503200EA7EEE /* HamsteriOS */ = {
			isa = XCSwiftPackageProductDependency;
			productName = HamsteriOS;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = C03E801D296D4F4C0083E53A /* Project object */;
}
