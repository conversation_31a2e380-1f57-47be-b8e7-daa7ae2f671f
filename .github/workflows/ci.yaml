name: Build LibrimeKit
on:
  workflow_dispatch:
  push:
    # branches:
      # - main
    tags:
      - '*'

jobs:
  build:
    runs-on: macos-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: Restore Cache Boost
        id: cache-boost-restore
        uses: actions/cache/restore@v3
        with:
          path: |
            /Users/<USER>/work/LibrimeKit/LibrimeKit/boost-iosx/frameworks
            /Users/<USER>/work/LibrimeKit/LibrimeKit/boost-iosx/dest
            /Users/<USER>/work/LibrimeKit/LibrimeKit/boost-iosx/scripts
          key: ${{ runner.os }}-boost

      - name: Build Boost
        if: (steps.cache-boost-restore.outputs.cache-hit != 'true')
        run: |
          make boost-build

      - name: copy Boost
        if: (steps.cache-boost-restore.outputs.cache-hit == 'true')
        run: |
          make boost-copy
      
      - name: Save Cache Boost
        id: cache-boost-save
        uses: actions/cache/save@v3
        with:
          path: |
            /Users/<USER>/work/LibrimeKit/LibrimeKit/boost-iosx/frameworks
            /Users/<USER>/work/LibrimeKit/LibrimeKit/boost-iosx/dest
            /Users/<USER>/work/LibrimeKit/LibrimeKit/boost-iosx/scripts
          key: ${{ runner.os }}-boost

      - name: Build Librime
        run: |
          make librime-build

      - name: Make Archive
        run: |
          tar -acf Frameworks.tgz Frameworks

      - name: Upload Archive
        uses: actions/upload-artifact@v3
        with:
          name: librimekit-frameworks
          path: Frameworks.tgz

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false

      - name: Upload Release Asset
        id: upload_release_asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: Frameworks.tgz
          asset_name: Frameworks.tgz
          asset_content_type: application/gzip
