# LibrimeKit API 文档

## 概述

LibrimeKit 是一个为 iOS 平台封装的中州韻輸入法引擎（Rime Input Method Engine）库，提供了完整的输入法功能支持。该项目将 librime 编译为 iOS 可用的静态库，并通过 Objective-C 和 Swift 提供了易于使用的 API 接口。

## 目录

- [快速开始](#快速开始)
- [安装配置](#安装配置)
- [核心架构](#核心架构)
- [API 参考](#api-参考)
- [数据模型](#数据模型)
- [高级功能](#高级功能)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)
- [常见使用场景](#常见使用场景)
- [性能优化指南](#性能优化指南)
- [安全考虑](#安全考虑)
- [部署指南](#部署指南)
- [版本兼容性](#版本兼容性)
- [常见问题 FAQ](#常见问题-faq)
- [完整示例项目](#完整示例项目)
- [平台支持](#平台支持)

## 快速开始

### 安装依赖

LibrimeKit 依赖以下二进制框架：
- `librime.xcframework` - 核心 Rime 引擎
- `boost_*.xcframework` - Boost 库组件
- `libglog.xcframework` - 日志库
- `libleveldb.xcframework` - 数据库
- `libmarisa.xcframework` - 字典树
- `libopencc.xcframework` - 简繁转换
- `libyaml-cpp.xcframework` - YAML 解析

### 基本使用

```swift
import RimeKit

// 1. 创建 Rime 配置
let traits = Rime.createTraits(
    sharedSupportDir: "/path/to/shared/support",
    userDataDir: "/path/to/user/data"
)

// 2. 启动 Rime 引擎
Rime.shared.start(traits, maintenance: true, fullCheck: true)

// 3. 处理输入
let handled = Rime.shared.inputKey("a")

// 4. 获取候选词
let candidates = Rime.shared.candidateList()

// 5. 获取提交文本
let commitText = Rime.shared.getCommitText()
```

## 5 分钟快速入门

如果您想快速体验 LibrimeKit，可以按照以下步骤创建一个最简单的示例：

### 创建新项目

1. 在 Xcode 中创建新的 iOS 项目
2. 选择 "App" 模板，语言选择 Swift

### 添加 LibrimeKit

1. 下载预编译的框架文件
2. 将 `RimeKit` 文件夹拖拽到项目中
3. 确保所有框架都已链接

### 最简示例代码

```swift
import UIKit
import RimeKit

class ViewController: UIViewController {
    @IBOutlet weak var inputTextField: UITextField!
    @IBOutlet weak var candidatesLabel: UILabel!
    @IBOutlet weak var outputLabel: UILabel!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupRime()
    }

    private func setupRime() {
        // 使用内置的测试配置
        let traits = Rime.createTraits(
            sharedSupportDir: Bundle.main.bundlePath,
            userDataDir: NSTemporaryDirectory() + "rime_test"
        )

        Rime.shared.start(traits)
        print("Rime 引擎已启动")
    }

    @IBAction func inputChanged(_ sender: UITextField) {
        guard let text = sender.text, !text.isEmpty else {
            candidatesLabel.text = ""
            return
        }

        // 处理输入
        let lastChar = String(text.last!)
        let handled = Rime.shared.inputKey(lastChar)

        if handled {
            // 获取候选词
            let candidates = Rime.shared.candidateList()
            let candidateTexts = candidates.prefix(5).map { $0.text }
            candidatesLabel.text = candidateTexts.joined(separator: " | ")
        }
    }

    @IBAction func selectFirstCandidate(_ sender: UIButton) {
        let success = Rime.shared.selectCandidate(index: 0)
        if success {
            let commitText = Rime.shared.getCommitText()
            outputLabel.text = (outputLabel.text ?? "") + commitText
            inputTextField.text = ""
            candidatesLabel.text = ""
        }
    }

    @IBAction func clearAll(_ sender: UIButton) {
        Rime.shared.cleanComposition()
        inputTextField.text = ""
        candidatesLabel.text = ""
        outputLabel.text = ""
    }
}
```

### 界面布局

在 Storyboard 中添加以下控件：

- `UITextField` - 输入框 (inputTextField)
- `UILabel` - 候选词显示 (candidatesLabel)
- `UILabel` - 输出结果 (outputLabel)
- `UIButton` - 选择候选词按钮
- `UIButton` - 清空按钮

### 运行测试

1. 运行应用
2. 在输入框中输入拼音字母（如 "ni hao"）
3. 观察候选词显示
4. 点击选择按钮确认输入

这个简单示例展示了 LibrimeKit 的基本用法，您可以在此基础上扩展更多功能。

## 安装配置

### 系统要求

在开始使用 LibrimeKit 之前，请确保您的开发环境满足以下要求：

- **macOS**: 12.0 或更高版本
- **Xcode**: 14.0 或更高版本
- **iOS 部署目标**: 15.0 或更高版本
- **Swift**: 5.8 或更高版本

### 步骤 1: 下载依赖框架

LibrimeKit 需要多个预编译的二进制框架。您可以通过以下方式获取：

#### 方法 1: 使用 Makefile（推荐）

```bash
# 在项目根目录执行
make framework
```

这将自动下载所有必需的框架文件到 `Hamster/Frameworks/` 目录。

#### 方法 2: 手动下载

从 [LibrimeKit Releases](https://github.com/imfuxiao/LibrimeKit/releases) 页面下载最新版本的框架文件，并解压到项目的 `Frameworks` 目录。

### 步骤 2: 项目集成

#### 使用 Swift Package Manager

1. 在 Xcode 中打开您的项目
2. 选择 `File` > `Add Package Dependencies`
3. 输入本地路径或 Git URL
4. 添加 `RimeKit` 作为依赖

```swift
// Package.swift
dependencies: [
    .package(path: "path/to/RimeKit")
]
```

#### 手动集成

1. 将 `RimeKit` 文件夹拖拽到您的 Xcode 项目中
2. 在项目设置的 `Build Phases` 中添加所有框架到 `Link Binary With Libraries`
3. 在 `Build Settings` 中设置 `Framework Search Paths` 指向框架目录

### 步骤 3: 配置 App Groups（键盘扩展必需）

如果您要开发键盘扩展，需要配置 App Groups 以便主应用和键盘扩展共享数据：

1. 在 Apple Developer Portal 创建 App Group
2. 在主应用和键盘扩展的 Capabilities 中启用 App Groups
3. 选择相同的 App Group ID

```swift
// 获取共享容器路径
let appGroupID = "group.com.yourcompany.rime"
let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupID)
let rimeDataPath = containerURL?.appendingPathComponent("Rime").path ?? ""
```

### 步骤 4: 准备 Rime 数据文件

#### 创建目录结构

```
YourApp/
├── SharedSupport/          # 共享支持文件
│   ├── *.schema.yaml      # 输入方案文件
│   ├── *.dict.yaml        # 词典文件
│   └── default.yaml       # 默认配置
└── UserData/              # 用户数据目录
    ├── default.custom.yaml # 用户自定义配置
    └── *.userdb/          # 用户词库
```

#### 基本配置文件示例

**default.yaml**:
```yaml
# 默认配置
schema_list:
  - schema: luna_pinyin
  - schema: double_pinyin

menu:
  page_size: 5

ascii_composer:
  good_old_caps_lock: true
  switch_key:
    Shift_L: inline_ascii
    Shift_R: commit_text
    Control_L: noop
    Control_R: noop
    Caps_Lock: clear
    Eisu_toggle: clear
```

**luna_pinyin.schema.yaml**:
```yaml
# 朙月拼音输入方案
schema:
  name: "朙月拼音"
  schema_id: luna_pinyin
  version: "0.25"
  author:
    - 佛振 <<EMAIL>>
  description: |
    朙月拼音，基于漢語拼音的中文輸入方案。

switches:
  - name: ascii_mode
    reset: 0
    states: [ 中文, 西文 ]
  - name: full_shape
    states: [ 半角, 全角 ]
  - name: simplification
    states: [ 漢字, 汉字 ]
  - name: ascii_punct
    states: [ 。，, ．， ]

engine:
  processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - punct_translator
    - script_translator
  filters:
    - simplifier
    - uniquifier

menu:
  page_size: 5

translator:
  dictionary: luna_pinyin
  prism: luna_pinyin
```

### 步骤 5: 初始化代码

在您的应用中添加初始化代码：

```swift
import RimeKit

class RimeManager {
    static let shared = RimeManager()

    private init() {
        setupRime()
    }

    private func setupRime() {
        // 获取资源路径
        let sharedSupportDir = getSharedSupportDirectory()
        let userDataDir = getUserDataDirectory()

        // 确保目录存在
        createDirectoryIfNeeded(sharedSupportDir)
        createDirectoryIfNeeded(userDataDir)

        // 创建配置
        let traits = Rime.createTraits(
            sharedSupportDir: sharedSupportDir,
            userDataDir: userDataDir,
            models: ["core", "dict", "gears", "levers"]
        )

        // 设置日志级别（开发时使用 0，生产时使用 2）
        traits.minLogLevel = 0

        // 启动引擎
        Rime.shared.start(traits, maintenance: true, fullCheck: true)

        print("Rime 引擎初始化完成")
    }

    private func getSharedSupportDirectory() -> String {
        // 开发环境：使用 Bundle 中的资源
        if let bundlePath = Bundle.main.path(forResource: "SharedSupport", ofType: nil) {
            return bundlePath
        }

        // 生产环境：使用 App Group 共享目录
        let appGroupID = "group.com.yourcompany.rime"
        if let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupID) {
            return containerURL.appendingPathComponent("SharedSupport").path
        }

        // 备用方案：使用 Documents 目录
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return "\(documentsPath)/SharedSupport"
    }

    private func getUserDataDirectory() -> String {
        let appGroupID = "group.com.yourcompany.rime"
        if let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupID) {
            return containerURL.appendingPathComponent("UserData").path
        }

        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return "\(documentsPath)/UserData"
    }

    private func createDirectoryIfNeeded(_ path: String) {
        let fileManager = FileManager.default
        if !fileManager.fileExists(atPath: path) {
            try? fileManager.createDirectory(atPath: path, withIntermediateDirectories: true, attributes: nil)
        }
    }
}
```

### 步骤 6: 验证安装

添加以下代码来验证 LibrimeKit 是否正确安装和配置：

```swift
func verifyRimeInstallation() {
    // 检查引擎是否运行
    guard Rime.shared.isRunning() else {
        print("❌ Rime 引擎未运行")
        return
    }

    // 检查可用方案
    let schemas = Rime.shared.getAvailableRimeSchemas()
    print("✅ 可用输入方案: \(schemas.map { $0.schemaName })")

    // 测试基本输入
    let handled = Rime.shared.inputKey("a")
    if handled {
        let candidates = Rime.shared.candidateList()
        print("✅ 输入测试成功，候选词数量: \(candidates.count)")

        // 清理测试输入
        Rime.shared.cleanComposition()
    } else {
        print("❌ 输入测试失败")
    }

    print("✅ LibrimeKit 安装验证完成")
}
```

### 常见安装问题

#### 问题 1: 框架找不到

**错误信息**: `dyld: Library not loaded: @rpath/librime.framework/librime`

**解决方案**:
1. 确保所有框架都已添加到项目中
2. 检查 `Build Settings` 中的 `Runpath Search Paths` 包含 `@executable_path/Frameworks`
3. 验证框架的 `Embed` 设置为 `Embed & Sign`

#### 问题 2: 编译错误

**错误信息**: `'RimeKit/RimeKit.h' file not found`

**解决方案**:
1. 确保 `RimeKit` 已正确添加为依赖
2. 检查 `Header Search Paths` 设置
3. 清理构建缓存 (`Product` > `Clean Build Folder`)

#### 问题 3: 运行时崩溃

**错误信息**: `EXC_BAD_ACCESS` 或 `SIGABRT`

**解决方案**:
1. 检查 Rime 数据文件是否存在且格式正确
2. 确保在主线程调用 Rime API
3. 验证目录权限和路径正确性

## 核心架构

### 架构层次

```
┌─────────────────────────────────────┐
│           Swift API Layer           │
│         (Rime.swift)               │
├─────────────────────────────────────┤
│        Objective-C Bridge          │
│       (IRimeAPI.h/.m)              │
├─────────────────────────────────────┤
│         C API Layer                │
│       (rime_api.h)                 │
├─────────────────────────────────────┤
│        librime Core Engine         │
│      (librime.xcframework)         │
└─────────────────────────────────────┘
```

### 主要组件

1. **Rime** - Swift 主要接口类，单例模式
2. **IRimeAPI** - Objective-C 桥接层
3. **数据模型** - 封装 Rime 数据结构
4. **通知系统** - 处理引擎状态变化

## API 参考

### Rime 类

#### 初始化和配置

##### `createTraits(sharedSupportDir:userDataDir:models:) -> IRimeTraits`

创建 Rime 引擎配置对象。

**参数：**
- `sharedSupportDir: String` - 共享支持目录路径
- `userDataDir: String` - 用户数据目录路径  
- `models: [String]` - 可选的模块列表，默认为空

**返回值：**
- `IRimeTraits` - 配置对象

**示例：**
```swift
let traits = Rime.createTraits(
    sharedSupportDir: "/app/shared",
    userDataDir: "/app/user",
    models: ["core", "dict", "gears", "lua"]
)
```

##### `setupRime(_:)` 和 `setupRime(sharedSupportDir:userDataDir:)`

设置 Rime 引擎配置。

**参数：**
- `traits: IRimeTraits` - 配置对象
- 或者直接传入目录路径

**注意：** 只在首次运行时执行，避免重复初始化导致的异常。

##### `start(_:maintenance:fullCheck:)`

启动 Rime 引擎。

**参数：**
- `traits: IRimeTraits?` - 可选配置对象
- `maintenance: Bool` - 是否执行维护模式，默认 false
- `fullCheck: Bool` - 是否执行完整检查，默认 false

#### 会话管理

##### `createSession()`

创建输入会话。如果会话不存在则创建新会话。

##### `getSession() -> RimeSessionId`

获取当前会话 ID。

##### `isRunning() -> Bool`

检查引擎是否正在运行。

##### `shutdown()`

关闭引擎并清理资源。

#### 输入处理

##### `inputKey(_:) -> Bool`

处理按键输入。

**参数：**
- `key: String` - 按键字符串

**返回值：**
- `Bool` - 是否成功处理

##### `inputKeyCode(_:modifier:) -> Bool`

处理按键码输入。

**参数：**
- `keycode: Int32` - 按键码
- `modifier: Int32` - 修饰键，默认 0

**返回值：**
- `Bool` - 是否成功处理

##### `replaceInputKeys(_:startPos:count:) -> Bool`

替换输入序列中的字符。

**参数：**
- `inputKeys: String` - 新的输入字符串
- `startPos: Int` - 开始位置
- `count: Int` - 替换字符数量

#### 候选词管理

##### `candidateList() -> [CandidateWord]`

获取当前所有候选词。

**返回值：**
- `[CandidateWord]` - 候选词数组

##### `candidateListWithIndex(index:andCount:) -> [CandidateWord]`

分页获取候选词。

**参数：**
- `index: Int` - 起始索引（从 0 开始）
- `count: Int` - 获取数量

**示例：**
```swift
// 第一页：获取前 10 个候选词
let page1 = Rime.shared.candidateListWithIndex(index: 0, andCount: 10)

// 第二页：获取第 11-20 个候选词  
let page2 = Rime.shared.candidateListWithIndex(index: 10, andCount: 10)
```

##### `selectCandidate(index:) -> Bool`

选择指定索引的候选词。

**参数：**
- `index: Int` - 候选词索引

##### `deleteCandidate(index:) -> Bool`

删除指定索引的候选词。

#### 文本获取

##### `getInputKeys() -> String`

获取当前输入的按键序列。

##### `getCommitText() -> String`

获取待提交的文本。

##### `cleanComposition()`

清空当前输入组合。

#### 输入方案管理

##### `getAvailableRimeSchemas() -> [RimeSchema]`

获取所有可用的输入方案。

**注意：** 用户目录必须存在 "default.custom.yaml" 文件。

##### `getSelectedRimeSchema() -> [RimeSchema]`

获取用户选择的输入方案列表。

##### `selectRimeSchemas(_:) -> Bool`

设置用户选择的输入方案。

**参数：**
- `schemas: [String]` - 方案 ID 数组

**注意：** 设置后需要重启引擎才能生效。

#### 选项控制

##### `isAsciiMode() -> Bool`

检查是否处于 ASCII 模式。

##### `setSimplifiedChineseMode(key:value:)`

设置简繁转换模式。

**参数：**
- `key: String` - 选项键名
- `value: Bool` - 选项值

##### `simplifiedChineseMode(key:) -> Bool`

获取简繁转换模式状态。

#### 光标控制

##### `getCaretPosition() -> Int`

获取光标位置。

##### `setCaretPosition(_:)`

设置光标位置。

**参数：**
- `position: Int` - 光标位置

#### 配置管理

##### `openSchema(_:) -> IRimeConfig`

打开指定输入方案的配置文件。

**参数：**
- `schema: String` - 方案 ID

**返回值：**
- `IRimeConfig` - 配置对象

**示例：**
```swift
let config = Rime.shared.openSchema("luna_pinyin")
let pageSize = config.getInt("menu/page_size")
```

##### `openConfig(_:) -> IRimeConfig`

打开指定的配置文件。

**参数：**
- `configId: String` - 配置文件 ID（不含扩展名）

##### `openUserConfig(_:) -> IRimeConfig`

打开用户数据目录中的配置文件。

**参数：**
- `configId: String` - 配置文件 ID

#### 自定义设置

##### `customize(_:boolValue:) -> Bool`

设置布尔类型的自定义配置。

**参数：**
- `key: String` - 配置键
- `value: Bool` - 配置值

##### `customize(_:stringValue:) -> Bool`

设置字符串类型的自定义配置。

**参数：**
- `key: String` - 配置键
- `value: String` - 配置值

##### `getCustomize(_:) -> String?`

获取自定义配置值。

**参数：**
- `key: String` - 配置键

**示例：**
```swift
// 设置自定义配置
let success = Rime.shared.API().customize("patch/ascii_mode", boolValue: true)

// 获取自定义配置
let value = Rime.shared.API().getCustomize("patch/ascii_mode")
```

#### 部署和维护

##### `deploy(_:) -> Bool`

部署 Rime 配置。

**参数：**
- `traits: IRimeTraits?` - 可选配置对象

##### `context() -> IRimeContext?`

获取当前输入上下文。

##### `runTask(_:) -> Bool`

运行指定的任务。

**参数：**
- `taskName: String` - 任务名称

##### `syncUserData() -> Bool`

同步用户数据。

##### `isFirstRun() -> Bool`

检查是否首次运行。

### 通知代理

#### IRimeNotificationDelegate 协议

```swift
protocol IRimeNotificationDelegate {
    func onDeployStart()           // 部署开始
    func onDeploySuccess()         // 部署成功  
    func onDeployFailure()         // 部署失败
    func onChangeMode(_ mode: String)      // 模式变化
    func onLoadingSchema(_ schema: String) // 加载方案
}
```

**使用示例：**
```swift
class MyRimeDelegate: IRimeNotificationDelegate {
    func onDeployStart() {
        print("开始部署...")
    }
    
    func onDeploySuccess() {
        print("部署成功")
    }
    
    func onDeployFailure() {
        print("部署失败")
    }
    
    func onChangeMode(_ mode: String) {
        print("模式变化: \(mode)")
    }
    
    func onLoadingSchema(_ schema: String) {
        print("加载方案: \(schema)")
    }
}

// 设置代理
let delegate = MyRimeDelegate()
Rime.shared.setNotificationDelegate(delegate)
```

## 数据模型

### CandidateWord

候选词数据结构。

```swift
public struct CandidateWord {
    public var text: String      // 候选文字
    public var comment: String   // 注释信息（如拼音）

    public init(text: String, comment: String)
}
```

### RimeSchema

输入方案数据结构。

```swift
public struct RimeSchema: Identifiable, Equatable, Hashable, Comparable, Codable {
    public var id: String           // 唯一标识符
    public var schemaId: String     // 方案 ID
    public var schemaName: String   // 方案名称

    public init(schemaId: String, schemaName: String)
}
```

### IRimeTraits

Rime 引擎配置参数。

```swift
@interface IRimeTraits : NSObject
@property NSString *sharedDataDir;        // 共享数据目录
@property NSString *userDataDir;          // 用户数据目录
@property NSString *distributionName;     // 发行版名称
@property NSString *distributionCodeName; // 发行版代号
@property NSString *distributionVersion;  // 发行版版本
@property NSString *appName;              // 应用名称
@property NSArray<NSString *> *modules;   // 模块列表
@property int minLogLevel;                // 最小日志级别 (0=INFO, 1=WARNING, 2=ERROR, 3=FATAL)
@property NSString *logDir;               // 日志目录
@property NSString *prebuiltDataDir;      // 预构建数据目录
@property NSString *stagingDir;           // 暂存目录
@end
```

### IRimeContext

输入上下文信息。

```swift
@interface IRimeContext : NSObject
@property NSString *commitTextPreview;           // 提交文本预览
@property IRimeMenu *menu;                       // 候选菜单
@property IRimeComposition *composition;         // 输入组合
@property NSArray<NSString *> *labels;          // 标签数组
@end
```

### IRimeComposition

输入组合信息。

```swift
@interface IRimeComposition : NSObject
@property int length;        // 长度
@property int cursorPos;     // 光标位置
@property int selStart;      // 选择开始位置
@property int selEnd;        // 选择结束位置
@property NSString *preedit; // 预编辑文本
@end
```

### IRimeMenu

候选菜单信息。

```swift
@interface IRimeMenu : NSObject
@property int pageSize;                          // 页面大小
@property int pageNo;                            // 页码
@property BOOL isLastPage;                       // 是否最后一页
@property int highlightedCandidateIndex;         // 高亮候选词索引
@property int numCandidates;                     // 候选词数量
@property NSString *selectKeys;                  // 选择键
@property NSArray<IRimeCandidate *> *candidates; // 候选词数组
@end
```

### IRimeStatus

引擎状态信息。

```swift
@interface IRimeStatus : NSObject
@property NSString *schemaId;     // 当前方案 ID
@property NSString *schemaName;   // 当前方案名称
@property BOOL isASCIIMode;       // 是否 ASCII 模式
@property BOOL isASCIIPunct;      // 是否 ASCII 标点
@property BOOL isComposing;       // 是否正在组合输入
@property BOOL isDisabled;        // 是否禁用
@property BOOL isFullShape;       // 是否全角
@property BOOL isSimplified;      // 是否简体
@property BOOL isTraditional;     // 是否繁体
@end
```

### IRimeConfig

配置文件操作接口。

```swift
@interface IRimeConfig : NSObject

- (NSString *)getString:(NSString *)key;
- (BOOL)getBool:(NSString *)key;
- (int)getInt:(NSString *)key;
- (BOOL)setInt:(NSString *)key value:(int) value;
- (double)getDouble:(NSString *)key;

- (NSArray<IRimeConfigIteratorItem *> *)getItems:(NSString *)key;
- (NSArray<IRimeConfigIteratorItem *> *)getMapValues:(NSString *)key;
- (void) closeConfig;
@end
```

**使用示例：**

```swift
// 打开方案配置
let config = Rime.shared.openSchema("luna_pinyin")

// 读取配置值
let pageSize = config.getInt("menu/page_size")
let schemaName = config.getString("schema/name")
let enableCompletion = config.getBool("translator/enable_completion")

// 获取列表项
let punctuatorItems = config.getItems("punctuator/full_shape")
for item in punctuatorItems {
    print("Key: \(item.key), Path: \(item.path)")
}

// 记得关闭配置
config.closeConfig()
```

### IRimeConfigIteratorItem

配置迭代器项目。

```swift
@interface IRimeConfigIteratorItem : NSObject
@property int index;        // 索引
@property NSString *key;    // 键名
@property NSString *path;   // 路径
@end
```

## 高级功能

### 用户词典管理

LibrimeKit 提供了用户词典的导入导出功能：

```swift
// 导出用户词典
let exportCount = Rime.shared.API().exportUserDict("luna_pinyin", textFile: "/path/to/export.txt")
print("导出了 \(exportCount) 个词条")

// 导入用户词典
let importCount = Rime.shared.API().importUserDict("luna_pinyin", textFile: "/path/to/import.txt")
print("导入了 \(importCount) 个词条")

// 备份用户词典
let backupSuccess = Rime.shared.API().backupUserDict("luna_pinyin")

// 恢复用户词典
let restoreSuccess = Rime.shared.API().restoreUserDict("/path/to/snapshot.userdb")
```

### 热键管理

```swift
// 获取当前热键配置
let hotkeys = Rime.shared.getHotkeys()
print("当前热键: \(hotkeys)")

// 设置热键（需要通过配置文件）
let config = Rime.shared.openUserConfig("default.custom")
// 修改配置后需要重新部署
```

### 状态标签

获取输入状态的显示标签：

```swift
let label = Rime.shared.API().getStateLabelAbbreviated(
    session: Rime.shared.getSession(),
    optionName: "ascii_mode",
    state: true,
    abbreviated: false
)
print("ASCII 模式标签: \(label)")
```

## 错误处理

### 日志系统

LibrimeKit 使用 OSLog 进行日志记录：

```swift
import OSLog

// 创建日志记录器
private let logger = Logger(
    subsystem: "com.ihsiao.apps.hamster.RimeKit",
    category: "Rime"
)

// 记录日志
logger.info("Rime engine started")
logger.error("Failed to process key: \(error)")
```

### 常见错误处理

1. **初始化错误**
   - 确保目录路径存在且可访问
   - 检查必要的配置文件是否存在

2. **会话错误**
   - 在调用输入方法前确保会话已创建
   - 定期检查会话状态

3. **部署错误**
   - 检查配置文件语法
   - 确保有足够的磁盘空间

### 错误检测示例

```swift
// 检查引擎状态
if !Rime.shared.isRunning() {
    logger.warning("Rime engine is not running")
    // 重新启动引擎
    Rime.shared.start(traits)
}

// 检查输入处理结果
let handled = Rime.shared.inputKey("a")
if !handled {
    logger.error("Failed to process input key")
}
```

## 最佳实践

### 1. 引擎生命周期管理

```swift
class InputMethodController {
    override func viewDidLoad() {
        super.viewDidLoad()
        setupRime()
    }

    private func setupRime() {
        let traits = Rime.createTraits(
            sharedSupportDir: getSharedSupportDir(),
            userDataDir: getUserDataDir()
        )

        // 设置通知代理
        Rime.shared.setNotificationDelegate(self)

        // 启动引擎
        Rime.shared.start(traits, maintenance: true)
    }

    deinit {
        // 清理资源
        Rime.shared.shutdown()
    }
}
```

### 2. 候选词分页处理

```swift
class CandidateManager {
    private let pageSize = 10
    private var currentPage = 0

    func loadNextPage() -> [CandidateWord] {
        let startIndex = currentPage * pageSize
        let candidates = Rime.shared.candidateListWithIndex(
            index: startIndex,
            andCount: pageSize
        )

        if !candidates.isEmpty {
            currentPage += 1
        }

        return candidates
    }

    func resetPaging() {
        currentPage = 0
    }
}
```

### 3. 输入方案切换

```swift
func switchToSchema(_ schemaId: String) {
    // 获取可用方案
    let availableSchemas = Rime.shared.getAvailableRimeSchemas()

    guard availableSchemas.contains(where: { $0.schemaId == schemaId }) else {
        logger.error("Schema not found: \(schemaId)")
        return
    }

    // 选择方案
    let success = Rime.shared.selectRimeSchemas([schemaId])
    if success {
        // 重启引擎使配置生效
        Rime.shared.shutdown()
        Rime.shared.start(traits, maintenance: true, fullCheck: true)
    }
}
```

### 4. 性能优化

```swift
// 使用缓存减少 API 调用
class RimeCache {
    private var candidateCache: [CandidateWord] = []
    private var lastInputKeys = ""

    func getCandidates() -> [CandidateWord] {
        let currentInputKeys = Rime.shared.getInputKeys()

        // 输入未变化时返回缓存
        if currentInputKeys == lastInputKeys && !candidateCache.isEmpty {
            return candidateCache
        }

        // 更新缓存
        candidateCache = Rime.shared.candidateList()
        lastInputKeys = currentInputKeys

        return candidateCache
    }
}
```

### 5. 内存管理

```swift
// 定期清理会话
class SessionManager {
    private var sessionCleanupTimer: Timer?

    func startSessionCleanup() {
        sessionCleanupTimer = Timer.scheduledTimer(withTimeInterval: 300) { _ in
            // 每5分钟清理一次无用会话
            if !Rime.shared.isRunning() {
                Rime.shared.createSession()
            }
        }
    }

    func stopSessionCleanup() {
        sessionCleanupTimer?.invalidate()
        sessionCleanupTimer = nil
    }
}
```

### 6. 配置文件管理

```swift
class RimeConfigManager {
    // 读取方案配置
    func getSchemaConfig(_ schemaId: String) -> [String: Any] {
        let config = Rime.shared.openSchema(schemaId)
        defer { config.closeConfig() }

        var result: [String: Any] = [:]
        result["name"] = config.getString("schema/name")
        result["version"] = config.getString("schema/version")
        result["author"] = config.getString("schema/author")
        result["description"] = config.getString("schema/description")
        result["page_size"] = config.getInt("menu/page_size")

        return result
    }

    // 修改用户配置
    func updateUserConfig(key: String, value: Any) -> Bool {
        if let stringValue = value as? String {
            return Rime.shared.API().customize(key, stringValue: stringValue)
        } else if let boolValue = value as? Bool {
            return Rime.shared.API().customize(key, boolValue: boolValue)
        }
        return false
    }

    // 批量更新配置
    func batchUpdateConfig(_ updates: [String: Any]) -> Bool {
        var allSuccess = true
        for (key, value) in updates {
            let success = updateUserConfig(key: key, value: value)
            if !success {
                allSuccess = false
                print("Failed to update config: \(key)")
            }
        }
        return allSuccess
    }
}
```

### 7. 输入法状态管理

```swift
class InputMethodStateManager {
    private var currentState: InputMethodState = .chinese

    enum InputMethodState {
        case chinese
        case ascii
        case halfWidth
        case fullWidth
    }

    func switchToASCIIMode() {
        Rime.shared.setSimplifiedChineseMode(key: "ascii_mode", value: true)
        currentState = .ascii
        notifyStateChange()
    }

    func switchToChineseMode() {
        Rime.shared.setSimplifiedChineseMode(key: "ascii_mode", value: false)
        currentState = .chinese
        notifyStateChange()
    }

    func toggleFullHalfWidth() {
        let currentFullShape = Rime.shared.API().getOption(
            Rime.shared.getSession(),
            andOption: "full_shape"
        )
        Rime.shared.API().setOption(
            Rime.shared.getSession(),
            andOption: "full_shape",
            andValue: !currentFullShape
        )
        currentState = currentFullShape ? .halfWidth : .fullWidth
        notifyStateChange()
    }

    private func notifyStateChange() {
        NotificationCenter.default.post(
            name: .inputMethodStateChanged,
            object: currentState
        )
    }
}

extension Notification.Name {
    static let inputMethodStateChanged = Notification.Name("inputMethodStateChanged")
}
```

### 8. 候选词处理优化

```swift
class CandidateProcessor {
    private let maxCandidates = 100
    private var candidateCache: [String: [CandidateWord]] = [:]

    func getOptimizedCandidates(for input: String) -> [CandidateWord] {
        // 检查缓存
        if let cached = candidateCache[input] {
            return cached
        }

        // 获取候选词
        let candidates = Rime.shared.candidateListWithIndex(
            index: 0,
            andCount: maxCandidates
        )

        // 过滤和排序
        let filtered = filterCandidates(candidates)
        let sorted = sortCandidatesByFrequency(filtered)

        // 缓存结果
        candidateCache[input] = sorted

        // 限制缓存大小
        if candidateCache.count > 50 {
            candidateCache.removeAll()
        }

        return sorted
    }

    private func filterCandidates(_ candidates: [CandidateWord]) -> [CandidateWord] {
        return candidates.filter { candidate in
            // 过滤空候选词
            !candidate.text.isEmpty &&
            // 过滤重复候选词
            !isDuplicate(candidate)
        }
    }

    private func sortCandidatesByFrequency(_ candidates: [CandidateWord]) -> [CandidateWord] {
        // 根据使用频率排序（这里简化处理）
        return candidates.sorted { first, second in
            // 优先显示较短的候选词
            if first.text.count != second.text.count {
                return first.text.count < second.text.count
            }
            // 其次按字母顺序
            return first.text < second.text
        }
    }

    private func isDuplicate(_ candidate: CandidateWord) -> Bool {
        // 检查是否为重复候选词的逻辑
        return false
    }
}
```

## 平台支持

### 支持的平台

- **iOS**: 15.0+
- **架构**: arm64, x86_64 (模拟器)

### 依赖要求

- **Swift**: 5.8+
- **Xcode**: 14.0+
- **部署目标**: iOS 15.0

### 二进制框架

所有依赖的二进制框架都以 XCFramework 格式提供，支持：

- iOS 设备 (arm64)
- iOS 模拟器 (arm64, x86_64)

### 集成方式

#### Swift Package Manager

```swift
dependencies: [
    .package(path: "path/to/RimeKit")
]
```

#### 手动集成

1. 下载预编译的框架文件
2. 将框架添加到项目中
3. 配置 Build Settings
4. 添加必要的链接库

### 注意事项

1. **静态库要求**: iOS App Store 不支持动态库，必须使用静态库版本
2. **文件权限**: 确保应用有读写用户数据目录的权限
3. **内存限制**: 在内存受限的环境中注意引擎的内存使用
4. **线程安全**: Rime API 不是线程安全的，需要在主线程调用

### 故障排除

#### 常见问题

1. **编译错误**
   - 检查 Swift 版本兼容性
   - 确保所有依赖框架都已正确链接

2. **运行时崩溃**
   - 检查目录路径是否正确
   - 确保配置文件格式正确

3. **输入无响应**
   - 检查会话是否已创建
   - 验证输入方案是否正确加载

4. **候选词为空**
   - 检查词库文件是否存在
   - 验证输入方案配置

#### 调试技巧

```swift
// 启用详细日志
let traits = Rime.createTraits(
    sharedSupportDir: sharedDir,
    userDataDir: userDir
)
traits.minLogLevel = 0  // INFO 级别
traits.logDir = "/path/to/logs"

// 检查引擎状态
let context = Rime.shared.context()
print("Current input: \(context?.composition?.preedit ?? "")")
print("Candidates count: \(context?.menu?.numCandidates ?? 0)")

// 调试会话状态
func debugSessionState() {
    let session = Rime.shared.getSession()
    let isRunning = Rime.shared.isRunning()
    let context = Rime.shared.context()

    print("=== Rime Debug Info ===")
    print("Session ID: \(session)")
    print("Is Running: \(isRunning)")
    print("Input Keys: \(Rime.shared.getInputKeys())")
    print("Commit Text: \(Rime.shared.getCommitText())")
    print("ASCII Mode: \(Rime.shared.isAsciiMode())")

    if let ctx = context {
        print("Composition: \(ctx.composition?.preedit ?? "nil")")
        print("Candidates: \(ctx.menu?.numCandidates ?? 0)")
        print("Current Page: \(ctx.menu?.pageNo ?? -1)")
    }
    print("=====================")
}

// 性能监控
func measureRimePerformance<T>(operation: () -> T) -> (result: T, time: TimeInterval) {
    let startTime = CFAbsoluteTimeGetCurrent()
    let result = operation()
    let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
    return (result, timeElapsed)
}

// 使用示例
let (candidates, time) = measureRimePerformance {
    return Rime.shared.candidateList()
}
print("获取候选词耗时: \(time * 1000)ms, 候选词数量: \(candidates.count)")
```

## 常见使用场景

### 1. 完整的输入法实现

```swift
class RimeInputMethod: NSObject {
    private let rime = Rime.shared
    private var isInitialized = false

    func initialize() {
        guard !isInitialized else { return }

        let traits = Rime.createTraits(
            sharedSupportDir: getSharedSupportDirectory(),
            userDataDir: getUserDataDirectory(),
            models: ["core", "dict", "gears", "levers"]
        )

        // 设置通知代理
        rime.setNotificationDelegate(self)

        // 启动引擎
        rime.start(traits, maintenance: true, fullCheck: false)

        isInitialized = true
    }

    func processInput(_ text: String) -> InputResult {
        guard isInitialized else {
            return InputResult(handled: false, commitText: "", candidates: [])
        }

        let handled = rime.inputKey(text)
        let commitText = rime.getCommitText()
        let candidates = rime.candidateList()

        return InputResult(
            handled: handled,
            commitText: commitText,
            candidates: candidates
        )
    }

    func selectCandidate(at index: Int) -> String? {
        let success = rime.selectCandidate(index: index)
        return success ? rime.getCommitText() : nil
    }

    func clearComposition() {
        rime.cleanComposition()
    }

    private func getSharedSupportDirectory() -> String {
        // 返回共享支持目录路径
        return Bundle.main.path(forResource: "SharedSupport", ofType: nil) ?? ""
    }

    private func getUserDataDirectory() -> String {
        // 返回用户数据目录路径
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return "\(documentsPath)/Rime"
    }
}

struct InputResult {
    let handled: Bool
    let commitText: String
    let candidates: [CandidateWord]
}

extension RimeInputMethod: IRimeNotificationDelegate {
    func onDeployStart() {
        print("开始部署 Rime 配置...")
    }

    func onDeploySuccess() {
        print("Rime 配置部署成功")
    }

    func onDeployFailure() {
        print("Rime 配置部署失败")
    }

    func onChangeMode(_ mode: String) {
        print("输入模式变更: \(mode)")
    }

    func onLoadingSchema(_ schema: String) {
        print("加载输入方案: \(schema)")
    }
}
```

### 2. 键盘扩展集成

```swift
import UIKit

class RimeKeyboardViewController: UIInputViewController {
    private let inputMethod = RimeInputMethod()
    private var candidateView: CandidateView!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupRime()
        setupUI()
    }

    private func setupRime() {
        inputMethod.initialize()
    }

    private func setupUI() {
        // 设置候选词视图
        candidateView = CandidateView()
        candidateView.delegate = self
        view.addSubview(candidateView)

        // 设置约束...
    }

    override func textWillChange(_ textInput: UITextInput?) {
        super.textWillChange(textInput)
    }

    override func textDidChange(_ textInput: UITextInput?) {
        super.textDidChange(textInput)
        updateCandidates()
    }

    private func handleKeyPress(_ key: String) {
        let result = inputMethod.processInput(key)

        if !result.commitText.isEmpty {
            textDocumentProxy.insertText(result.commitText)
        }

        if result.handled {
            updateCandidatesView(with: result.candidates)
        }
    }

    private func updateCandidates() {
        let candidates = inputMethod.rime.candidateList()
        updateCandidatesView(with: candidates)
    }

    private func updateCandidatesView(with candidates: [CandidateWord]) {
        candidateView.updateCandidates(candidates)
    }
}

extension RimeKeyboardViewController: CandidateViewDelegate {
    func didSelectCandidate(at index: Int) {
        if let commitText = inputMethod.selectCandidate(at: index) {
            textDocumentProxy.insertText(commitText)
            updateCandidates()
        }
    }
}
```

### 3. 配置管理界面

```swift
class RimeSettingsViewController: UIViewController {
    @IBOutlet weak var schemaTableView: UITableView!

    private var availableSchemas: [RimeSchema] = []
    private var selectedSchemas: [RimeSchema] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        loadSchemas()
    }

    private func loadSchemas() {
        availableSchemas = Rime.shared.getAvailableRimeSchemas()
        selectedSchemas = Rime.shared.getSelectedRimeSchema()
        schemaTableView.reloadData()
    }

    @IBAction func saveSettings(_ sender: UIButton) {
        let schemaIds = selectedSchemas.map { $0.schemaId }
        let success = Rime.shared.selectRimeSchemas(schemaIds)

        if success {
            // 重启 Rime 使配置生效
            Rime.shared.shutdown()
            let traits = Rime.createTraits(
                sharedSupportDir: getSharedSupportDir(),
                userDataDir: getUserDataDir()
            )
            Rime.shared.start(traits, maintenance: true, fullCheck: true)

            showAlert(title: "成功", message: "输入方案配置已保存")
        } else {
            showAlert(title: "错误", message: "保存配置失败")
        }
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}
```

### 4. 九宫格输入法实现

九宫格输入法（T9 输入法）是移动设备上常见的输入方式。以下是完整的实现方案：

#### T9 键位映射

```swift
class T9InputManager {
    // T9 键位映射表
    private let t9KeyMap: [String: String] = [
        "2": "abc",
        "3": "def",
        "4": "ghi",
        "5": "jkl",
        "6": "mno",
        "7": "pqrs",
        "8": "tuv",
        "9": "wxyz"
    ]

    // 反向映射：字母到数字
    private let letterToNumberMap: [Character: String] = {
        var map: [Character: String] = [:]
        let keyMap = [
            "2": "abc",
            "3": "def",
            "4": "ghi",
            "5": "jkl",
            "6": "mno",
            "7": "pqrs",
            "8": "tuv",
            "9": "wxyz"
        ]

        for (number, letters) in keyMap {
            for letter in letters {
                map[letter] = number
            }
        }
        return map
    }()

    // 将拼音转换为 T9 数字序列
    func convertPinyinToT9(_ pinyin: String) -> String {
        return pinyin.lowercased().compactMap { char in
            letterToNumberMap[char]
        }.joined()
    }

    // 将 T9 数字序列转换为可能的拼音组合
    func convertT9ToPinyinCombinations(_ t9Input: String) -> [String] {
        guard !t9Input.isEmpty else { return [] }

        var combinations: [String] = [""]

        for digit in t9Input {
            let digitStr = String(digit)
            guard let letters = t9KeyMap[digitStr] else { continue }

            var newCombinations: [String] = []
            for combination in combinations {
                for letter in letters {
                    newCombinations.append(combination + String(letter))
                }
            }
            combinations = newCombinations
        }

        return combinations
    }
}
```

#### T9 输入处理器

```swift
class T9RimeInputProcessor {
    private let t9Manager = T9InputManager()
    private let rime = Rime.shared
    private var currentT9Input = ""
    private var candidateCache: [String: [CandidateWord]] = [:]

    // 处理 T9 数字输入
    func processT9Input(_ digit: String) -> T9InputResult {
        // 验证输入是否为有效的 T9 数字
        guard ["2", "3", "4", "5", "6", "7", "8", "9"].contains(digit) else {
            return T9InputResult(handled: false, candidates: [], inputDisplay: currentT9Input)
        }

        currentT9Input += digit

        // 检查缓存
        if let cachedCandidates = candidateCache[currentT9Input] {
            return T9InputResult(
                handled: true,
                candidates: cachedCandidates,
                inputDisplay: currentT9Input
            )
        }

        // 生成候选词
        let candidates = generateT9Candidates(currentT9Input)
        candidateCache[currentT9Input] = candidates

        return T9InputResult(
            handled: true,
            candidates: candidates,
            inputDisplay: currentT9Input
        )
    }

    // 生成 T9 候选词
    private func generateT9Candidates(_ t9Input: String) -> [CandidateWord] {
        var allCandidates: [CandidateWord] = []

        // 获取所有可能的拼音组合
        let pinyinCombinations = t9Manager.convertT9ToPinyinCombinations(t9Input)

        // 为每个拼音组合获取候选词
        for pinyin in pinyinCombinations.prefix(10) { // 限制组合数量以提高性能
            // 清空当前输入
            rime.cleanComposition()

            // 输入拼音
            var inputSuccess = true
            for char in pinyin {
                if !rime.inputKey(String(char)) {
                    inputSuccess = false
                    break
                }
            }

            if inputSuccess {
                let candidates = rime.candidateList()
                allCandidates.append(contentsOf: candidates)
            }
        }

        // 去重并排序
        let uniqueCandidates = removeDuplicateCandidates(allCandidates)
        return Array(uniqueCandidates.prefix(20)) // 限制候选词数量
    }

    // 选择候选词
    func selectT9Candidate(_ candidate: CandidateWord) -> String? {
        // 找到对应的拼音并设置到 Rime
        let pinyinCombinations = t9Manager.convertT9ToPinyinCombinations(currentT9Input)

        for pinyin in pinyinCombinations {
            rime.cleanComposition()

            // 输入拼音
            for char in pinyin {
                rime.inputKey(String(char))
            }

            // 检查候选词列表
            let candidates = rime.candidateList()
            if let index = candidates.firstIndex(where: { $0.text == candidate.text }) {
                let success = rime.selectCandidate(index: index)
                if success {
                    let commitText = rime.getCommitText()
                    clearT9Input()
                    return commitText
                }
            }
        }

        return nil
    }

    // 清空 T9 输入
    func clearT9Input() {
        currentT9Input = ""
        candidateCache.removeAll()
        rime.cleanComposition()
    }

    // 删除最后一个 T9 输入
    func deleteLastT9Input() -> T9InputResult {
        guard !currentT9Input.isEmpty else {
            return T9InputResult(handled: false, candidates: [], inputDisplay: "")
        }

        currentT9Input.removeLast()

        if currentT9Input.isEmpty {
            return T9InputResult(handled: true, candidates: [], inputDisplay: "")
        }

        return processT9Input("")  // 重新处理当前输入
    }

    // 去重候选词
    private func removeDuplicateCandidates(_ candidates: [CandidateWord]) -> [CandidateWord] {
        var seen = Set<String>()
        var uniqueCandidates: [CandidateWord] = []

        for candidate in candidates {
            if !seen.contains(candidate.text) {
                seen.insert(candidate.text)
                uniqueCandidates.append(candidate)
            }
        }

        return uniqueCandidates
    }
}

// T9 输入结果
struct T9InputResult {
    let handled: Bool
    let candidates: [CandidateWord]
    let inputDisplay: String  // 显示给用户的输入内容
}
```

#### T9 键盘界面实现

```swift
class T9KeyboardView: UIView {
    private let inputProcessor = T9RimeInputProcessor()
    private var candidateView: T9CandidateView!
    private var inputDisplayLabel: UILabel!

    weak var delegate: T9KeyboardDelegate?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = UIColor.systemGray6

        // 输入显示标签
        inputDisplayLabel = UILabel()
        inputDisplayLabel.textAlignment = .center
        inputDisplayLabel.font = UIFont.systemFont(ofSize: 18)
        inputDisplayLabel.backgroundColor = UIColor.systemBackground
        addSubview(inputDisplayLabel)

        // 候选词视图
        candidateView = T9CandidateView()
        candidateView.delegate = self
        addSubview(candidateView)

        // T9 键盘按钮
        setupT9Buttons()

        // 设置约束
        setupConstraints()
    }

    private func setupT9Buttons() {
        let buttonTitles = [
            ("1", ""), ("2", "ABC"), ("3", "DEF"),
            ("4", "GHI"), ("5", "JKL"), ("6", "MNO"),
            ("7", "PQRS"), ("8", "TUV"), ("9", "WXYZ"),
            ("*", ""), ("0", ""), ("#", "")
        ]

        for (i, (number, letters)) in buttonTitles.enumerated() {
            let button = createT9Button(number: number, letters: letters)
            button.tag = i
            button.addTarget(self, action: #selector(t9ButtonTapped(_:)), for: .touchUpInside)
            addSubview(button)
        }

        // 删除按钮
        let deleteButton = UIButton(type: .system)
        deleteButton.setTitle("删除", for: .normal)
        deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
        addSubview(deleteButton)
    }

    private func createT9Button(number: String, letters: String) -> UIButton {
        let button = UIButton(type: .system)
        button.backgroundColor = UIColor.systemBackground
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemGray4.cgColor

        // 设置按钮文本
        let attributedTitle = NSMutableAttributedString()
        attributedTitle.append(NSAttributedString(
            string: number,
            attributes: [
                .font: UIFont.boldSystemFont(ofSize: 24),
                .foregroundColor: UIColor.label
            ]
        ))

        if !letters.isEmpty {
            attributedTitle.append(NSAttributedString(string: "\n"))
            attributedTitle.append(NSAttributedString(
                string: letters,
                attributes: [
                    .font: UIFont.systemFont(ofSize: 12),
                    .foregroundColor: UIColor.secondaryLabel
                ]
            ))
        }

        button.setAttributedTitle(attributedTitle, for: .normal)
        button.titleLabel?.numberOfLines = 2
        button.titleLabel?.textAlignment = .center

        return button
    }

    @objc private func t9ButtonTapped(_ sender: UIButton) {
        let buttonTitles = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "*", "0", "#"]
        let digit = buttonTitles[sender.tag]

        // 只处理 2-9 的数字键
        guard ["2", "3", "4", "5", "6", "7", "8", "9"].contains(digit) else { return }

        let result = inputProcessor.processT9Input(digit)
        updateUI(with: result)
    }

    @objc private func deleteButtonTapped() {
        let result = inputProcessor.deleteLastT9Input()
        updateUI(with: result)
    }

    private func updateUI(with result: T9InputResult) {
        inputDisplayLabel.text = result.inputDisplay.isEmpty ? "请输入" : result.inputDisplay
        candidateView.updateCandidates(result.candidates)
    }

    private func setupConstraints() {
        // 设置自动布局约束
        // 这里简化处理，实际项目中需要详细设置
    }
}

// T9 键盘代理
protocol T9KeyboardDelegate: AnyObject {
    func t9Keyboard(_ keyboard: T9KeyboardView, didCommitText text: String)
}

// T9 候选词视图
class T9CandidateView: UIView {
    private var candidates: [CandidateWord] = []
    private var collectionView: UICollectionView!

    weak var delegate: T9CandidateViewDelegate?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupCollectionView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCollectionView()
    }

    private func setupCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 60, height: 40)
        layout.minimumInteritemSpacing = 8

        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor.systemBackground
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(T9CandidateCell.self, forCellWithReuseIdentifier: "CandidateCell")

        addSubview(collectionView)

        // 设置约束
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }

    func updateCandidates(_ newCandidates: [CandidateWord]) {
        candidates = newCandidates
        collectionView.reloadData()
    }
}

extension T9CandidateView: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return candidates.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "CandidateCell", for: indexPath) as! T9CandidateCell
        cell.configure(with: candidates[indexPath.item])
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let candidate = candidates[indexPath.item]
        delegate?.t9CandidateView(self, didSelectCandidate: candidate)
    }
}

protocol T9CandidateViewDelegate: AnyObject {
    func t9CandidateView(_ view: T9CandidateView, didSelectCandidate candidate: CandidateWord)
}

// T9 候选词单元格
class T9CandidateCell: UICollectionViewCell {
    private let label = UILabel()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = UIColor.systemGray5
        layer.cornerRadius = 8

        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 16)
        addSubview(label)

        label.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: centerXAnchor),
            label.centerYAnchor.constraint(equalTo: centerYAnchor)
        ])
    }

    func configure(with candidate: CandidateWord) {
        label.text = candidate.text
    }
}

extension T9CandidateView: T9CandidateViewDelegate {
    func t9CandidateView(_ view: T9CandidateView, didSelectCandidate candidate: CandidateWord) {
        // 处理候选词选择
        // 这里需要与 T9InputProcessor 配合
    }
}
```

#### 使用示例

```swift
class T9InputViewController: UIViewController {
    private var t9Keyboard: T9KeyboardView!
    private var outputTextView: UITextView!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupRime()
    }

    private func setupUI() {
        // 输出文本视图
        outputTextView = UITextView()
        outputTextView.font = UIFont.systemFont(ofSize: 18)
        outputTextView.isEditable = false
        view.addSubview(outputTextView)

        // T9 键盘
        t9Keyboard = T9KeyboardView()
        t9Keyboard.delegate = self
        view.addSubview(t9Keyboard)

        // 设置约束...
    }

    private func setupRime() {
        let traits = Rime.createTraits(
            sharedSupportDir: getSharedSupportDir(),
            userDataDir: getUserDataDir()
        )
        Rime.shared.start(traits)
    }
}

extension T9InputViewController: T9KeyboardDelegate {
    func t9Keyboard(_ keyboard: T9KeyboardView, didCommitText text: String) {
        outputTextView.text += text
    }
}
```

这个实现提供了完整的九宫格输入法功能，包括：

1. **T9 数字到拼音的转换**
2. **候选词生成和显示**
3. **用户界面组件**
4. **输入状态管理**

用户输入数字序列（如 "64"）时，系统会：
1. 转换为可能的拼音组合（如 "ni"）
2. 通过 Rime 引擎获取候选词（如 "你"、"尼"、"泥"）
3. 显示候选词供用户选择
4. 用户选择后提交到文本输出

---

## 性能优化指南

### 1. 内存优化

```swift
class RimeMemoryManager {
    private var configCache: [String: IRimeConfig] = [:]
    private let maxCacheSize = 10

    func getCachedConfig(_ schemaId: String) -> IRimeConfig? {
        if let config = configCache[schemaId] {
            return config
        }

        let config = Rime.shared.openSchema(schemaId)

        // 限制缓存大小
        if configCache.count >= maxCacheSize {
            // 清理最旧的配置
            let oldestKey = configCache.keys.first!
            configCache[oldestKey]?.closeConfig()
            configCache.removeValue(forKey: oldestKey)
        }

        configCache[schemaId] = config
        return config
    }

    func clearCache() {
        for config in configCache.values {
            config.closeConfig()
        }
        configCache.removeAll()
    }
}
```

### 2. 启动优化

```swift
class RimeFastStartup {
    private static let preloadedSchemas = ["luna_pinyin", "double_pinyin"]

    static func optimizedStartup() {
        let traits = Rime.createTraits(
            sharedSupportDir: getSharedDir(),
            userDataDir: getUserDir(),
            models: ["core", "dict", "gears"] // 只加载必要模块
        )

        // 异步启动以避免阻塞主线程
        DispatchQueue.global(qos: .userInitiated).async {
            Rime.shared.start(traits, maintenance: false, fullCheck: false)

            // 预加载常用方案
            DispatchQueue.main.async {
                preloadSchemas()
            }
        }
    }

    private static func preloadSchemas() {
        for schemaId in preloadedSchemas {
            let config = Rime.shared.openSchema(schemaId)
            // 预读取一些配置以加载到内存
            _ = config.getString("schema/name")
            config.closeConfig()
        }
    }
}
```

### 3. 候选词优化

```swift
class OptimizedCandidateManager {
    private let batchSize = 20
    private var candidateBuffer: [CandidateWord] = []
    private var currentIndex = 0

    func getNextBatch() -> [CandidateWord] {
        if candidateBuffer.isEmpty || currentIndex >= candidateBuffer.count {
            loadMoreCandidates()
        }

        let endIndex = min(currentIndex + batchSize, candidateBuffer.count)
        let batch = Array(candidateBuffer[currentIndex..<endIndex])
        currentIndex = endIndex

        return batch
    }

    private func loadMoreCandidates() {
        let newCandidates = Rime.shared.candidateListWithIndex(
            index: candidateBuffer.count,
            andCount: batchSize * 2
        )
        candidateBuffer.append(contentsOf: newCandidates)
    }

    func reset() {
        candidateBuffer.removeAll()
        currentIndex = 0
    }
}
```

## 安全考虑

### 1. 数据保护

```swift
class RimeSecurityManager {
    // 加密敏感配置
    func encryptUserConfig(_ config: [String: Any]) -> Data? {
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: config)
            return try encryptData(jsonData)
        } catch {
            print("配置加密失败: \(error)")
            return nil
        }
    }

    // 解密配置
    func decryptUserConfig(_ encryptedData: Data) -> [String: Any]? {
        do {
            let decryptedData = try decryptData(encryptedData)
            return try JSONSerialization.jsonObject(with: decryptedData) as? [String: Any]
        } catch {
            print("配置解密失败: \(error)")
            return nil
        }
    }

    private func encryptData(_ data: Data) throws -> Data {
        // 实现数据加密逻辑
        return data // 简化示例
    }

    private func decryptData(_ data: Data) throws -> Data {
        // 实现数据解密逻辑
        return data // 简化示例
    }
}
```

### 2. 输入验证

```swift
class RimeInputValidator {
    private let maxInputLength = 100
    private let allowedCharacters = CharacterSet.alphanumerics.union(.punctuationCharacters)

    func validateInput(_ input: String) -> Bool {
        // 检查长度
        guard input.count <= maxInputLength else {
            return false
        }

        // 检查字符集
        let inputCharacterSet = CharacterSet(charactersIn: input)
        guard allowedCharacters.isSuperset(of: inputCharacterSet) else {
            return false
        }

        return true
    }

    func sanitizeInput(_ input: String) -> String {
        let sanitized = input.components(separatedBy: allowedCharacters.inverted).joined()
        return String(sanitized.prefix(maxInputLength))
    }
}
```

## 部署指南

### 1. 生产环境部署

```swift
class RimeProductionSetup {
    static func setupForProduction() {
        let traits = Rime.createTraits(
            sharedSupportDir: getProductionSharedDir(),
            userDataDir: getProductionUserDir()
        )

        // 生产环境配置
        traits.minLogLevel = 2 // 只记录错误日志
        traits.logDir = getLogDirectory()

        // 启动引擎
        Rime.shared.start(traits, maintenance: false, fullCheck: false)

        // 设置错误处理
        setupErrorHandling()
    }

    private static func setupErrorHandling() {
        // 设置全局错误处理
        NSSetUncaughtExceptionHandler { exception in
            print("Rime 异常: \(exception)")
            // 记录到日志文件
            logError("Uncaught exception: \(exception)")
        }
    }

    private static func getProductionSharedDir() -> String {
        return Bundle.main.path(forResource: "RimeData", ofType: nil) ?? ""
    }

    private static func getProductionUserDir() -> String {
        let appSupport = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        return appSupport.appendingPathComponent("Rime").path
    }

    private static func getLogDirectory() -> String {
        let logs = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        return logs.appendingPathComponent("Logs").path
    }

    private static func logError(_ message: String) {
        let timestamp = DateFormatter().string(from: Date())
        let logMessage = "[\(timestamp)] \(message)\n"

        let logFile = URL(fileURLWithPath: getLogDirectory()).appendingPathComponent("rime_errors.log")

        if let data = logMessage.data(using: .utf8) {
            if FileManager.default.fileExists(atPath: logFile.path) {
                if let fileHandle = try? FileHandle(forWritingTo: logFile) {
                    fileHandle.seekToEndOfFile()
                    fileHandle.write(data)
                    fileHandle.closeFile()
                }
            } else {
                try? data.write(to: logFile)
            }
        }
    }
}
```

### 2. 测试环境配置

```swift
class RimeTestSetup {
    static func setupForTesting() {
        let traits = Rime.createTraits(
            sharedSupportDir: getTestSharedDir(),
            userDataDir: getTestUserDir()
        )

        // 测试环境配置
        traits.minLogLevel = 0 // 详细日志
        traits.logDir = getTestLogDir()

        Rime.shared.start(traits, maintenance: true, fullCheck: true)
    }

    static func createMockData() {
        // 创建测试用的配置文件
        let testConfig = """
        schema:
          name: "测试方案"
          schema_id: test_schema
          version: "1.0"

        menu:
          page_size: 5

        translator:
          dictionary: test_dict
        """

        let configPath = getTestUserDir() + "/test_schema.schema.yaml"
        try? testConfig.write(toFile: configPath, atomically: true, encoding: .utf8)
    }

    private static func getTestSharedDir() -> String {
        return NSTemporaryDirectory() + "RimeTest/shared"
    }

    private static func getTestUserDir() -> String {
        return NSTemporaryDirectory() + "RimeTest/user"
    }

    private static func getTestLogDir() -> String {
        return NSTemporaryDirectory() + "RimeTest/logs"
    }
}
```

## 版本兼容性

### 支持的版本

| 组件 | 最低版本 | 推荐版本 | 最新测试版本 |
|------|----------|----------|--------------|
| iOS | 15.0 | 16.0+ | 17.0 |
| Swift | 5.8 | 5.9+ | 5.10 |
| Xcode | 14.0 | 15.0+ | 15.4 |
| librime | 1.8.5 | 1.9.0+ | 1.10.0 |

### 版本检查

```swift
class RimeVersionChecker {
    static func checkCompatibility() -> Bool {
        // 检查 iOS 版本
        if #available(iOS 15.0, *) {
            print("iOS 版本兼容")
        } else {
            print("iOS 版本过低，需要 15.0 或更高版本")
            return false
        }

        // 检查 librime 版本
        let rimeVersion = getRimeVersion()
        if compareVersion(rimeVersion, "1.8.5") >= 0 {
            print("librime 版本兼容: \(rimeVersion)")
        } else {
            print("librime 版本过低: \(rimeVersion)")
            return false
        }

        return true
    }

    private static func getRimeVersion() -> String {
        // 获取 librime 版本信息
        return "1.9.0" // 示例版本
    }

    private static func compareVersion(_ version1: String, _ version2: String) -> Int {
        let v1Components = version1.split(separator: ".").compactMap { Int($0) }
        let v2Components = version2.split(separator: ".").compactMap { Int($0) }

        let maxCount = max(v1Components.count, v2Components.count)

        for i in 0..<maxCount {
            let v1 = i < v1Components.count ? v1Components[i] : 0
            let v2 = i < v2Components.count ? v2Components[i] : 0

            if v1 < v2 { return -1 }
            if v1 > v2 { return 1 }
        }

        return 0
    }
}
```

## 版本历史

- **v2.0+**: 重构项目，将 API 封装移至 Hamster 项目的 RimeKit 中
- **v1.x**: 包含完整的 Swift Package Manager 支持和 API 封装

## 许可证

请参考项目根目录的 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 常见问题 (FAQ)

### Q: 如何解决编译时找不到框架的问题？

**A:** 确保所有依赖的 XCFramework 都已正确添加到项目中：

1. 检查 `Frameworks` 目录下是否包含所有必要的框架
2. 在 Xcode 项目设置中验证 `Framework Search Paths`
3. 确保 `Build Phases` 中的 `Link Binary With Libraries` 包含所有框架

### Q: 为什么调用 `candidateList()` 返回空数组？

**A:** 可能的原因：

1. 会话未正确创建：调用 `createSession()` 确保会话存在
2. 输入方案未加载：检查方案文件是否存在且格式正确
3. 词库文件缺失：确保相应的词典文件已部署

```swift
// 调试代码
if !Rime.shared.isRunning() {
    print("引擎未运行")
    Rime.shared.createSession()
}

let inputKeys = Rime.shared.getInputKeys()
print("当前输入: \(inputKeys)")

if inputKeys.isEmpty {
    print("没有输入内容，无法生成候选词")
}
```

### Q: 如何处理内存泄漏问题？

**A:** 注意以下几点：

1. 及时关闭配置对象：
```swift
let config = Rime.shared.openSchema("schema_id")
defer { config.closeConfig() }
```

2. 在适当时机关闭引擎：
```swift
deinit {
    Rime.shared.shutdown()
}
```

3. 清理缓存：
```swift
// 定期清理候选词缓存
candidateCache.removeAll()
```

### Q: 如何自定义输入方案？

**A:** 创建自定义方案文件：

1. 在 `SharedSupport` 目录创建 `.schema.yaml` 文件
2. 定义方案配置：

```yaml
# custom_schema.schema.yaml
schema:
  name: "自定义方案"
  schema_id: custom_schema
  version: "1.0"
  author: "Your Name"
  description: "自定义输入方案"

switches:
  - name: ascii_mode
    reset: 0
    states: [ 中文, 西文 ]
  - name: full_shape
    states: [ 半角, 全角 ]

engine:
  processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - punct_translator
    - script_translator

menu:
  page_size: 5

translator:
  dictionary: custom_dict
```

3. 创建对应的词典文件 `custom_dict.dict.yaml`

### Q: 如何处理多线程访问？

**A:** Rime API 不是线程安全的，需要在主线程调用：

```swift
class ThreadSafeRimeWrapper {
    private let queue = DispatchQueue.main

    func safeInputKey(_ key: String, completion: @escaping (Bool) -> Void) {
        queue.async {
            let result = Rime.shared.inputKey(key)
            completion(result)
        }
    }

    func safeCandidateList(completion: @escaping ([CandidateWord]) -> Void) {
        queue.async {
            let candidates = Rime.shared.candidateList()
            completion(candidates)
        }
    }
}
```

### Q: 如何优化启动速度？

**A:** 采用以下策略：

1. 延迟初始化：
```swift
class LazyRimeManager {
    private var isInitialized = false

    func ensureInitialized() {
        guard !isInitialized else { return }

        DispatchQueue.global(qos: .userInitiated).async {
            self.initializeRime()
            DispatchQueue.main.async {
                self.isInitialized = true
            }
        }
    }
}
```

2. 预加载关键配置：
```swift
func preloadEssentialConfigs() {
    let essentialSchemas = ["luna_pinyin", "double_pinyin"]
    for schema in essentialSchemas {
        let config = Rime.shared.openSchema(schema)
        // 预读取配置
        _ = config.getString("schema/name")
        config.closeConfig()
    }
}
```

### Q: 如何实现自定义候选词排序？

**A:** 实现自定义排序逻辑：

```swift
class CustomCandidateSorter {
    func sortCandidates(_ candidates: [CandidateWord]) -> [CandidateWord] {
        return candidates.sorted { first, second in
            // 1. 优先显示常用词
            let firstFrequency = getWordFrequency(first.text)
            let secondFrequency = getWordFrequency(second.text)

            if firstFrequency != secondFrequency {
                return firstFrequency > secondFrequency
            }

            // 2. 其次按长度排序
            if first.text.count != second.text.count {
                return first.text.count < second.text.count
            }

            // 3. 最后按字母顺序
            return first.text < second.text
        }
    }

    private func getWordFrequency(_ word: String) -> Int {
        // 实现词频统计逻辑
        return UserDefaults.standard.integer(forKey: "freq_\(word)")
    }
}
```

## 完整示例项目

以下是一个完整的 iOS 输入法项目示例：

### 项目结构

```
RimeInputMethod/
├── RimeInputMethod/
│   ├── AppDelegate.swift
│   ├── ViewController.swift
│   └── RimeManager.swift
├── RimeKeyboard/
│   ├── KeyboardViewController.swift
│   ├── CandidateView.swift
│   └── KeyboardView.swift
├── Shared/
│   ├── RimeWrapper.swift
│   └── Models.swift
└── Resources/
    ├── SharedSupport/
    └── Frameworks/
```

### 核心实现

```swift
// RimeWrapper.swift
import RimeKit

class RimeWrapper: ObservableObject {
    static let shared = RimeWrapper()

    @Published var candidates: [CandidateWord] = []
    @Published var inputText: String = ""
    @Published var isASCIIMode: Bool = false

    private let rime = Rime.shared
    private var isInitialized = false

    private init() {
        setupRime()
    }

    private func setupRime() {
        let traits = Rime.createTraits(
            sharedSupportDir: getSharedSupportPath(),
            userDataDir: getUserDataPath(),
            models: ["core", "dict", "gears", "levers"]
        )

        rime.setNotificationDelegate(self)
        rime.start(traits, maintenance: true, fullCheck: false)
        isInitialized = true
    }

    func processInput(_ input: String) -> Bool {
        guard isInitialized else { return false }

        let handled = rime.inputKey(input)
        updateState()
        return handled
    }

    func selectCandidate(at index: Int) -> String? {
        guard index < candidates.count else { return nil }

        let success = rime.selectCandidate(index: index)
        if success {
            let commitText = rime.getCommitText()
            updateState()
            return commitText
        }
        return nil
    }

    func clearInput() {
        rime.cleanComposition()
        updateState()
    }

    func toggleASCIIMode() {
        isASCIIMode.toggle()
        rime.setSimplifiedChineseMode(key: "ascii_mode", value: isASCIIMode)
        updateState()
    }

    private func updateState() {
        DispatchQueue.main.async {
            self.candidates = self.rime.candidateList()
            self.inputText = self.rime.getInputKeys()
            self.isASCIIMode = self.rime.isAsciiMode()
        }
    }

    private func getSharedSupportPath() -> String {
        return Bundle.main.path(forResource: "SharedSupport", ofType: nil) ?? ""
    }

    private func getUserDataPath() -> String {
        let appGroup = "group.com.yourcompany.rime"
        let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroup)
        return containerURL?.appendingPathComponent("Rime").path ?? ""
    }
}

extension RimeWrapper: IRimeNotificationDelegate {
    func onDeployStart() {
        print("开始部署...")
    }

    func onDeploySuccess() {
        print("部署成功")
        updateState()
    }

    func onDeployFailure() {
        print("部署失败")
    }

    func onChangeMode(_ mode: String) {
        print("模式变更: \(mode)")
        updateState()
    }

    func onLoadingSchema(_ schema: String) {
        print("加载方案: \(schema)")
    }
}
```

## 相关链接

- [librime](https://github.com/rime/librime) - 中州韻輸入法引擎
- [boost-iosx](https://github.com/imfuxiao/boost-iosx) - iOS 平台的 Boost 库
- [Hamster](https://github.com/imfuxiao/Hamster) - 使用 LibrimeKit 的输入法应用
- [Rime 官方文档](https://rime.im/docs/) - Rime 输入法引擎官方文档
- [iOS 键盘扩展开发指南](https://developer.apple.com/documentation/uikit/keyboards_and_input) - Apple 官方文档

## 总结

LibrimeKit 为 iOS 平台提供了完整的中文输入法解决方案。通过本文档，您应该能够：

### 已掌握的技能

✅ **基础使用**
- 安装和配置 LibrimeKit
- 创建和启动 Rime 引擎
- 处理基本的输入和候选词

✅ **进阶功能**
- 自定义输入方案配置
- 管理用户词典
- 实现候选词分页
- 处理输入法状态切换

✅ **最佳实践**
- 性能优化策略
- 内存管理技巧
- 错误处理机制
- 安全考虑事项

✅ **实际应用**
- 完整的输入法应用开发
- 键盘扩展集成
- 配置管理界面实现

### 下一步建议

1. **开始实践**: 使用 5 分钟快速入门创建您的第一个 LibrimeKit 应用
2. **深入学习**: 研究完整示例项目，了解更复杂的实现
3. **自定义扩展**: 根据需求定制输入方案和词库
4. **性能优化**: 应用最佳实践提升应用性能
5. **社区参与**: 关注项目更新，参与社区讨论

### 获取帮助

如果在使用过程中遇到问题：

1. 首先查阅 [常见问题 FAQ](#常见问题-faq) 部分
2. 检查 [故障排除](#故障排除) 指南
3. 在 GitHub 项目页面提交 Issue
4. 参考 Rime 官方文档获取更多输入法配置信息

### 贡献代码

欢迎为 LibrimeKit 项目做出贡献：

- 报告 Bug 和问题
- 提交功能请求
- 改进文档内容
- 提供代码补丁

---

**感谢您选择 LibrimeKit！**

希望这份文档能帮助您快速上手并充分利用 LibrimeKit 的强大功能。如果您有任何建议或反馈，请随时联系我们。
